package router

import (
	"ai_cr/service"
	"ai_cr/setup"
	"context"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/xanzy/go-gitlab"
)

var mr = &Mr{}

type Mr struct {
}

func (p *Mr) TestCase(c *gin.Context) {
	caseS := c.Query("case")
	switch caseS {

	}
	return
}

func (p *Mr) AiCr(c *gin.Context) {

	pID := c.Query("pID")
	mrIIDStr := c.Query("mrID")
	namespace := c.Query("namespace")

	//
	mrIID := cast.ToInt(mrIIDStr)
	ctx := c.Request.Context()
	gitClient, err := setup.GetGitlabClientByName(namespace)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取 GitLab 客户端失败: " + err.Error()})
		return
	}

	service.Get(ctx).MrApi.MrProcess(ctx, gitClient, pID, mrIID)
}

// GitlabHook 实现基于 GitLab webhook 的 MR 处理逻辑
// 参考: https://gitlab.cn/docs/jh/user/project/integrations/webhook_events.html#merge-request-events
func (p *Mr) GitlabHook(c *gin.Context) {
	// 获取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	isDebug := c.Query("debug")
	// 使用 go-gitlab 库的 ParseWebhook 函数解析 webhook 数据
	event, err := gitlab.ParseWebhook(gitlab.EventTypeMergeRequest, body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无法解析 webhook 数据: " + err.Error()})
		return
	}

	// 处理合并请求事件
	switch event := event.(type) {
	case *gitlab.MergeEvent:
		// 从事件中直接获取项目ID和合并请求ID
		projectID := cast.ToString(event.Project.ID)
		mrIID := event.ObjectAttributes.IID

		// 可能需要其他条件过滤，例如只处理打开状态的合并请求
		if event.ObjectAttributes.State == "opened" || event.ObjectAttributes.State == "reopened" {
			ctx := c.Request.Context()
			gitClient, err := setup.GetGitlabClientByName(event.Project.Namespace)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "获取 GitLab 客户端失败: " + err.Error()})
				return
			}
			if len(isDebug) > 0 {
				service.Get(ctx).MrApi.MrProcess(ctx, gitClient, projectID, mrIID)
			} else {
				go func(ctx context.Context, gitClient *gitlab.Client, projectID string, mrIID int) {
					service.Get(ctx).MrApi.MrProcess(ctx, gitClient, projectID, mrIID)
				}(ctx, gitClient, projectID, mrIID)
			}

			c.JSON(http.StatusOK, gin.H{"status": "success", "message": "MR处理已触发"})
		} else {
			c.JSON(http.StatusOK, gin.H{"status": "skipped", "message": "MR状态不需要处理"})
		}
		return
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "不支持的事件类型"})
		return
	}
	//ctx := c.Request.Context()
	//service.Get(ctx).MrApi.MrProcess(ctx, pID, mrIID)
}

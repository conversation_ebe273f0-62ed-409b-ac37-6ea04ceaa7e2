package setup

import (
	"errors"
	"fmt"
	"github.com/xanzy/go-gitlab"
	"os"
	"sync"
)

type GitlabClientType string

const (
	GitlabDefault GitlabClientType = "backend"              // 默认客户端
	GitlabWuhan                    = "wuhan"                // 武汉客户端
	GitlabTeacher                  = "teacher"              // 教师客户端
	GitlabPlat                     = "security-and-payment" // 平台客户端
)

// gitlabClientNames 客户端类型名称映射
var gitlabClientNames = map[GitlabClientType]string{
	GitlabDefault: "后端项目",
	Gitlab<PERSON>uhan:   "wuhan",
	GitlabTeacher: "teacher",
	GitlabPlat:    "中台基础服务",
}

// GetGitlabClientByName 根据对应的名字gitlabClientNames  获取对应的客户端链接
func GetGitlabClientByName(clientName string) (*gitlab.Client, error) {
	// 遍历gitlabClientNames映射，查找匹配的客户端名称
	for clientType, name := range gitlabClientNames {
		if name == clientName {
			// 找到匹配的客户端名称，使用对应的clientType获取客户端
			return GetGitlabClient(clientType), nil
		}
	}
	// 如果没有找到匹配的客户端名称，返回默认客户端
	fmt.Printf("警告: 未找到名称为 %s 的客户端，使用默认客户端\n", clientName)
	return nil, errors.New("未找到对应的客户端")
}

// gitlabTokenEnvVars 客户端类型与环境变量的映射
var gitlabTokenEnvVars = map[GitlabClientType]string{
	GitlabDefault: "YC_GITLAB_TOKEN",
	GitlabWuhan:   "YC_GITLAB_WUHAN_TOKEN",
	GitlabTeacher: "YC_GITLAB_TEACHER_TOKEN",
	GitlabPlat:    "YC_GITLAB_PLAT_TOKEN",
}

// String 实现 Stringer 接口
func (t GitlabClientType) String() string {
	if name, ok := gitlabClientNames[t]; ok {
		return name
	}
	return "未知"
}

var (
	GitlabClient     *gitlab.Client                              // 默认客户端
	gitlabClients    = make(map[GitlabClientType]*gitlab.Client) // 所有客户端
	gitClientMutex   sync.Mutex
	gitClientInitMap = make(map[GitlabClientType]*sync.Once)
)

func init() {
	// 为每种客户端类型初始化一个 sync.Once
	for clientType := range gitlabTokenEnvVars {
		gitClientInitMap[clientType] = &sync.Once{}
	}
}

// InitGitlabClient 初始化默认GitLab客户端
func InitGitlabClient() {
	// 初始化默认客户端
	client := GetGitlabClient(GitlabDefault)
	GitlabClient = client
}

// GetGitlabClient 根据客户端类型获取对应的GitLab客户端
func GetGitlabClient(clientType GitlabClientType) *gitlab.Client {
	gitClientMutex.Lock()
	defer gitClientMutex.Unlock()

	// 如果已经初始化了该类型的客户端，直接返回
	if client, exists := gitlabClients[clientType]; exists && client != nil {
		return client
	}

	// 使用 sync.Once 确保每种类型的客户端只初始化一次
	gitClientInitMap[clientType].Do(func() {
		envVar := gitlabTokenEnvVars[clientType]
		token := os.Getenv(envVar)
		if token == "" {
			fmt.Printf("警告: 环境变量 %s 未设置，无法初始化%s GitLab客户端\n", envVar, clientType.String())
			return
		}

		opt := gitlab.WithBaseURL("https://gitlab.yc345.tv/api/v4")
		client, err := gitlab.NewClient(token, opt)
		if err != nil {
			fmt.Printf("创建%s GitLab客户端错误: %v\n", clientType.String(), err)
			return
		}
		gitlabClients[clientType] = client
	})

	return gitlabClients[clientType]
}

package setup

import (
	"ai_cr/conf"
	"fmt"
	"sync"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var AiProblemPgConn *gorm.DB
var AiProblemPgOnce sync.Once

func InitPg() {
	var err error
	AiProblemPgOnce.Do(func() {
		dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=disable TimeZone=Asia/Shanghai",
			conf.Detail().GetString("aipgsql.host"),
			conf.Detail().GetString("aipgsql.user"),
			conf.Detail().GetString("aipgsql.password"),
			conf.Detail().GetString("aipgsql.dbname"),
			conf.Detail().GetInt("aipgsql.port"),
		)
		AiProblemPgConn, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Info),
		})
		if err != nil {
			panic(err)
		}
	})

}

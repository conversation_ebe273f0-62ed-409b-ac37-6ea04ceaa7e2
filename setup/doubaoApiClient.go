package setup

import (
	"errors"
	"os"
	"sync"

	"github.com/volcengine/volcengine-go-sdk/service/arkruntime"
)

var DoubaoApiClient *arkruntime.Client
var doubaoApiOnce sync.Once

// InitDaubaoApi es客户端初始化
func InitDaubaoApi() {
	doubaoApiOnce.Do(func() {
		DoubaoApiClient = arkruntime.NewClientWithApiKey(
			os.Getenv("ARK_API_KEY"),
			arkruntime.WithBaseUrl("https://ark.cn-beijing.volces.com/api/v3"),
			arkruntime.WithRegion("cn-beijing"))
		if DoubaoApiClient == nil {
			panic(errors.New("doubaoApiClient init failed"))
		}
	})
}

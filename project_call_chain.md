# 项目调用链路图

## 主要调用流程

![alt text](image.png)

```mermaid
flowchart TD
    A[main.go] --> B[InitService]
    B --> C[conf.StartConfig]
    B --> D[setup.InitLog]
    B --> E[setup.InitDaubaoApi]
    B --> F[setup.InitGitlabClient]
    B --> G[setup.InitPg]
    
    A --> H[router.InitRouter]
    H --> I[mr.TestCase]
    H --> J[mr.AiCr]
    
    J --> K[service.MrProcess]
    K --> L[GitLab API 获取 MR 详情]
    K --> M[file_repo.FetchFileContent]
    K --> N[file_repo.ParseSingleFile]
    K --> O[service.AiCr]
    
    O --> P[GetR1Completion AI 评审]
    
    K --> Q[service.ReplyForCr]
    Q --> R[GitLab API 添加评论]
```

## 关键模块职责

1. **main.go**：
   - 应用入口
   - 初始化服务
   - 配置路由
   - 启动 Web 服务器

2. **setup 包**：
   - 初始化日志
   - 初始化豆包 API 客户端
   - 初始化 GitLab 客户端
   - 初始化 PostgreSQL 数据库连接

3. **router 包**：
   - 定义路由规则
   - 将 HTTP 请求映射到对应的处理函数

4. **service/mr.go**：
   - 处理合并请求
   - 获取代码差异
   - 解析代码文件
   - 执行 AI 代码审查
   - 在 GitLab MR 中添加评论

5. **service/file_repo/func_op.go**：
   - 获取文件内容
   - 解析代码文件
   - 提取函数信息

## 代码评审报告

### 优点
1. 模块职责清晰，各层次解耦
2. 使用依赖注入方式初始化服务
3. 代码审查流程自动化
4. 支持多种代码文件解析

### 待改进点
1. 缺少详细的日志记录
2. 硬编码的文件过滤逻辑
3. 缺少配置文件管理过滤规则
4. AI 评审结果处理缺乏灵活性

### 优化建议
1. 使用更精细的错误处理机制
2. 增加结构化日志记录
3. 将文件过滤规则提取到配置文件
4. 增加 AI 评审结果的可配置性
5. 添加性能监控和追踪

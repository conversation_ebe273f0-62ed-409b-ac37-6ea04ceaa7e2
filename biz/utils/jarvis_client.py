import os
import requests
from typing import Optional, Dict, Any, List
from biz.utils.log import logger


class JarvisClient:
    """JARVIS服务客户端，用于调用JARVIS后端API"""

    def __init__(self):
        self.base_url = os.getenv('JARVIS_BASE_URL', 'http://localhost:8400')
        self.timeout = int(os.getenv('JARVIS_TIMEOUT', '10'))

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Optional[Dict[Any, Any]]:
        """发起HTTP请求的通用方法"""
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"

        try:
            response = requests.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"JARVIS API请求失败: {response.status_code}, {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"JARVIS API请求异常: {str(e)}")
            return None

    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """根据邮箱获取用户信息"""
        if not email:
            return None

        logger.info(f"查询JARVIS用户信息，邮箱: {email}")

        result = self._make_request(
            'GET',
            '/user/email',
            params={'email': email.strip()}
        )

        # 检查请求是否成功
        if not result:
            logger.info(f"未找到邮箱为 {email} 的JARVIS用户信息 - API请求失败")
            return None

        # 获取数据部分
        data = result.get('data')
        if not data:
            logger.info(f"未找到邮箱为 {email} 的JARVIS用户信息 - 响应中无data字段")
            return None

        if data:
            logger.info(f"成功获取JARVIS用户信息: {data.get('name', 'Unknown')}")

        return data

    def get_groups_by_user_email(self, email: str) -> Optional[Dict[str, Any]]:
        """根据用户邮箱获取用户所在的AICR群配置"""
        if not email:
            return None

        logger.info(f"查询用户AICR群配置，邮箱: {email}")

        result = self._make_request(
            'GET',
            '/aicr/users/email/groups',
            params={'email': email.strip()}
        )

        # 检查请求是否成功
        if not result:
            logger.info(f"未找到邮箱为 {email} 的用户群配置 - API请求失败")
            return None

        # 检查响应状态
        if result.get('error', False) or result.get('code', 0) != 0:
            logger.warning(f"JARVIS API返回错误，邮箱: {email}, code: {result.get('code')}, error: {result.get('error')}")
            return None

        # 获取数据部分
        data = result.get('data')
        if not data:
            logger.info(f"未找到邮箱为 {email} 的用户群配置 - 响应中无data字段")
            return None

        # 检查用户信息
        user_info = data.get('user')
        groups = data.get('groups', [])

        if user_info:
            groups_count = len(groups)
            user_name = user_info.get('name', 'Unknown')
            logger.info(f"成功获取用户群配置，用户: {user_name}, 群数量: {groups_count}")
        else:
            logger.info(f"未找到邮箱为 {email} 的用户群配置 - 响应中无用户信息")
            return None

        return data

    def get_groups_by_project_id(self, project_id: str) -> Optional[List[Dict[str, Any]]]:
        """根据项目ID获取对应的AICR群配置"""
        if not project_id:
            return None

        logger.info(f"查询项目AICR群配置，项目ID: {project_id}")

        result = self._make_request(
            'GET',
            f'/aicr/projects/{project_id.strip()}/groups'
        )

        # 检查请求是否成功
        if not result:
            logger.info(f"未找到项目ID为 {project_id} 的群配置 - API请求失败")
            return None

        # 检查响应状态
        if result.get('error', False) or result.get('code', 0) != 0:
            logger.warning(f"JARVIS API返回错误，项目ID: {project_id}, code: {result.get('code')}, error: {result.get('error')}")
            return None

        # 获取数据部分
        data = result.get('data')
        if data is None:
            logger.info(f"未找到项目ID为 {project_id} 的群配置 - 响应中无data字段")
            return None

        # 确保data是列表
        if not isinstance(data, list):
            logger.warning(f"项目群配置数据格式错误，项目ID: {project_id}, data类型: {type(data)}")
            return None

        # 记录成功日志
        groups_count = len(data)
        if groups_count > 0:
            logger.info(f"成功获取项目群配置，项目ID: {project_id}, 群数量: {groups_count}")
        else:
            logger.info(f"项目ID为 {project_id} 的群配置为空")

        return data


class GitlabUserClient:
    """GitLab用户信息客户端，用于获取提交人的详细信息"""

    def __init__(self, gitlab_url: str, gitlab_token: str):
        self.gitlab_url = gitlab_url.rstrip('/')
        self.gitlab_token = gitlab_token
        self.timeout = int(os.getenv('GITLAB_TIMEOUT', '10'))

    def get_commit_author_email(self, project_id: str, commit_sha: str) -> Optional[str]:
        """根据项目ID和提交SHA获取提交人邮箱"""
        if not project_id or not commit_sha:
            return None

        url = f"{self.gitlab_url}/api/v4/projects/{project_id}/repository/commits/{commit_sha}"
        headers = {
            'Private-Token': self.gitlab_token
        }

        try:
            logger.info(f"获取GitLab提交详情，项目: {project_id}, 提交: {commit_sha}")

            response = requests.get(url, headers=headers, timeout=self.timeout, verify=False)

            if response.status_code == 200:
                commit_data = response.json()
                author_email = commit_data.get('author_email')

                if author_email:
                    logger.info(f"成功获取提交人邮箱: {author_email}")
                else:
                    logger.warning(f"提交信息中未找到author_email字段")

                return author_email
            else:
                logger.warning(f"获取GitLab提交详情失败: {response.status_code}, {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"GitLab API请求异常: {str(e)}")
            return None

    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """根据用户名获取用户详细信息"""
        if not username:
            return None

        url = f"{self.gitlab_url}/api/v4/users"
        headers = {
            'Private-Token': self.gitlab_token
        }
        params = {
            'username': username
        }

        try:
            logger.info(f"根据用户名查询GitLab用户信息: {username}")

            response = requests.get(url, headers=headers, params=params, timeout=self.timeout, verify=False)

            if response.status_code == 200:
                users = response.json()
                if users and len(users) > 0:
                    user = users[0]
                    email = user.get('email')
                    if email:
                        logger.info(f"成功获取用户邮箱: {email}")
                    else:
                        logger.warning(f"用户信息中未找到email字段")
                    return user
                else:
                    logger.warning(f"未找到用户名为 {username} 的用户")
                    return None
            else:
                logger.warning(f"查询GitLab用户信息失败: {response.status_code}, {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"GitLab API请求异常: {str(e)}")
            return None

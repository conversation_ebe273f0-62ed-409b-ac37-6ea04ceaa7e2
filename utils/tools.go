package utils

func JaccardDiff(a1, a2 []string) float64 {
	set1 := make(map[string]bool)
	set2 := make(map[string]bool)

	// Create a set from a1
	for _, item := range a1 {
		set1[item] = true
	}

	// Create a set from a2
	for _, item := range a2 {
		set2[item] = true
	}

	// Calculate the intersection and union
	intersectionSize := 0
	unionSize := len(set1) // Start with the size of set1

	for item := range set2 {
		if set1[item] { // If item is also in set1, it's an intersection
			intersectionSize++
		} else { // If item is not in set1, it adds to the union
			unionSize++
		}
	}

	if unionSize == 0 { // Prevent division by zero
		return 1 // If both are empty, we say they are perfectly similar
	}

	return float64(intersectionSize) / float64(unionSize)
}

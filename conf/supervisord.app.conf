[supervisord]
nodaemon=true
user=root

[program:flask]
command=python /app/api.py
autostart=true
autorestart=true
numprocs=1
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_maxbytes=0
stderr_maxbytes=0
stdout_logfile_maxbytes = 0
stderr_logfile_maxbytes = 0

[program:streamlit]
command=streamlit run /app/ui.py --server.port=5002 --server.address=0.0.0.0 --server.headless true
autostart=true
autorestart=true
numprocs=1
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_maxbytes=0
stderr_maxbytes=0
stdout_logfile_maxbytes = 0
stderr_logfile_maxbytes = 0
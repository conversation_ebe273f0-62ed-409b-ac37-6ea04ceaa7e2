FROM docker.yc345.tv/mirror/golang:1.23.7-alpine3.21 AS builder

ADD . /root
WORKDIR /root


# 使用go mod下载依赖并编译
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    GOPROXY="https://goproxy.cn,direct"  go build -o bin/ai_cr main.go

FROM docker.yc345.tv/mirror/golang:1.23.7-alpine3.21

COPY --from=builder /root/bin/ai_cr /app/ai_cr

COPY ./conf /src/conf

WORKDIR /app

EXPOSE 8000

CMD ["./ai_cr", "-conf", "/src/conf"] 
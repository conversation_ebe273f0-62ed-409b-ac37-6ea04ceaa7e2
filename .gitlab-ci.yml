# 配置文档
# https://guanghe.feishu.cn/docs/doccn7x1etOkjKfKZL9mwQjtpzf#

# Harbor
# https://docker.yc345.tv/harbor/projects/5/repositories

# k8s 管理UI
# https://rancher.yc345.tv/

# 定义项目构建或部署过程中使用的环境变量
variables:
  #  DOCKER_HOST: tcp://docker-dind:2375
  DOCKER_REGISTRY: docker.yc345.tv
  K8S_DEPLOYMENT: ai-cr
  NAMESPACE: 7to12
  HOME: /builds

stages:
  - build_test
  - deploy_test
  - build_pro

.tag_testenv_k8s: &testenv_k8s_tag
  tags:
    - testenv
    - kubernetes
.before_script: &before_script
  before_script:
    - export GO111MODULE=on
    - export HOME=/root
    - export GOPROXY=https://goproxy.cn,direct
    - export GOPRIVATE=gitlab.yc345.tv
    - |
      cat <<-EOF >> ~/.gitconfig
      [url "**************:"]
          insteadOf = https://github.com/
      [url "*******************:"]
          insteadOf = https://gitlab.yc345.tv/
      EOF
    - mkdir ~/.ssh && chmod 700 ~/.ssh && cat $PRIVATE_KEY > ~/.ssh/id_rsa && chmod 600 ~/.ssh/id_rsa
    - |
      cat <<-EOF >> ~/.ssh/config
      Host *
      StrictHostKeyChecking no
      UserKnownHostsFile=/dev/null
      EOF
    - chmod 600 ~/.ssh/config

【测试环境】镜像构建:
  stage: build_test
  image: docker:20.10.21-dind-alpine3.16
  only:
    - test
  before_script:
    - mkdir .ssh && chmod 700 .ssh && cat $Private_REPO_SSHKey > .ssh/id_rsa && chmod 600 .ssh/id_rsa
  script:
    - echo $CI_PROJECT_DIR
    - docker login --username=$DOCKER_USERNAME $DOCKER_REGISTRY -p $DOCKER_PASSWORD
    - docker build -t $DOCKER_REGISTRY/$NAMESPACE/$K8S_DEPLOYMENT:$CI_COMMIT_SHORT_SHA .
    - docker push $DOCKER_REGISTRY/$NAMESPACE/$K8S_DEPLOYMENT:$CI_COMMIT_SHORT_SHA
【测试环境】容器发布:
  stage: deploy_test
  image: bitnami/kubectl:1.20.1
  only:
    - test
  script:
    - kubectl config set-cluster test-cluster --server=https://localk8s.yc345.tv:6443 --insecure-skip-tls-verify=true
    - kubectl config set-credentials gitlab-ci --token=$KUBE_TOKEN_TEST
    - kubectl config set-context testenv --cluster=test-cluster --user=gitlab-ci && kubectl config use-context testenv
    - kubectl -n $NAMESPACE set image deployment $K8S_DEPLOYMENT $K8S_DEPLOYMENT=$DOCKER_REGISTRY/$NAMESPACE/$K8S_DEPLOYMENT:$CI_COMMIT_SHORT_SHA --record
    - kubectl -n $NAMESPACE rollout status deployment $K8S_DEPLOYMENT
【正式环境】镜像构建:
  stage: build_pro
  image: docker:20.10.21-dind-alpine3.16
  only:
    - /^[v]?\d+\.\d+\.\d+$/
  before_script:
    - mkdir .ssh && chmod 700 .ssh && cat $Private_REPO_SSHKey > .ssh/id_rsa && chmod 600 .ssh/id_rsa
  script:
    - docker login --username=$DOCKER_USERNAME $DOCKER_REGISTRY -p $DOCKER_PASSWORD
    - docker build -t $DOCKER_REGISTRY/$NAMESPACE/$K8S_DEPLOYMENT:${CI_COMMIT_TAG} --build-arg buildENV=production .
    - docker push $DOCKER_REGISTRY/$NAMESPACE/$K8S_DEPLOYMENT:${CI_COMMIT_TAG}

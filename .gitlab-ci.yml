variables:
  NAMESPACE: 7to12
  DOCKER_REGISTRY: docker.yc345.tv

stages:
  - build_test
  - build_prod

before_script:
  - git config --global http.https://github.com.proxy http://********:8118 && git config --global https.https://github.com.proxy http://********:8118

build_test:
  stage: build_test
  only:
    - feat/supportAICR
  tags:
    - ops
  script:
    # 生成测试环境镜像tag格式：版本号_fe_test
    - export IMAGE_TAG="${CI_COMMIT_TAG:-${CI_COMMIT_SHORT_SHA}}_fe_test"
    # Docker 构建和推送
    - docker login --username=$DOCKER_USERNAME $DOCKER_REGISTRY -p $DOCKER_PASSWORD
    - docker build -f Dockerfile --target app -t $DOCKER_REGISTRY/$NAMESPACE/aicr:$IMAGE_TAG .
    - docker push $DOCKER_REGISTRY/$NAMESPACE/aicr:$IMAGE_TAG
    # Kubernetes 部署到测试环境
    - kubectl config set-cluster test-cluster --server=$KUBE_HOST_TEST --insecure-skip-tls-verify=true
    - kubectl config set-credentials gitlab-ci --token=$KUBE_TOKEN_TEST
    - kubectl config set-context testenv --cluster=test-cluster --user=gitlab-ci && kubectl config use-context testenv
    - kubectl -n $NAMESPACE set image deployment ai-codereview-fe-test ai-codereview-fe-test=$DOCKER_REGISTRY/$NAMESPACE/aicr:$IMAGE_TAG --record
    - kubectl -n $NAMESPACE rollout status deployment ai-codereview-fe-test

build_prod:
  stage: build_prod
  only:
    refs:
      - frontend
    variables:
      - $CI_COMMIT_TAG
  tags:
    - ops
  script:
    # 生成生产环境镜像tag格式：版本号_fe
    - export IMAGE_TAG="${CI_COMMIT_TAG}_fe"
    # Docker 构建和推送（使用 tag 名称）
    - docker login --username=$DOCKER_USERNAME $DOCKER_REGISTRY -p $DOCKER_PASSWORD
    - docker build -f Dockerfile --target app -t $DOCKER_REGISTRY/$NAMESPACE/aicr:$IMAGE_TAG .
    - docker push $DOCKER_REGISTRY/$NAMESPACE/aicr:$IMAGE_TAG
    # Kubernetes 部署到生产环境
    - kubectl config set-cluster prod-cluster --server=$KUBE_HOST_PROD --insecure-skip-tls-verify=true
    - kubectl config set-credentials gitlab-ci --token=$KUBE_TOKEN_PROD
    - kubectl config set-context prodenv --cluster=prod-cluster --user=gitlab-ci && kubectl config use-context prodenv
    - kubectl -n $NAMESPACE set image deployment ai-codereview-fe ai-codereview-fe=$DOCKER_REGISTRY/$NAMESPACE/ai-codereview-fe:$IMAGE_TAG --record
    - kubectl -n $NAMESPACE rollout status deployment ai-codereview-fe

package repo

import (
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

func GetChatCompletion(systemPrompt, usePrompt string) string {
	client := resty.New()
	requestBody := map[string]interface{}{
		"model": "gpt-3.5-turbo",
		"messages": []map[string]string{
			{
				"role":    "system",
				"content": systemPrompt,
			},
			{
				"role":    "user",
				"content": usePrompt,
			},
		},
	}

	// Perform the request
	// token use: xi-ai.cn for-aicase
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Authorization", "Bearer sk-Z0ss5k2q3zB7DE8k2cF4000cC9F1413e9f05DcCe9eE22937").
		SetBody(requestBody).
		Post("https://api.xi-ai.cn/v1/chat/completions")

	if err != nil {
		fmt.Println("Error:", err)
		return ""
	}
	result := ""
	// Print the response
	fmt.Println("Response Body:", resp.String())

	gjson.Get(resp.String(), "choices").ForEach(func(_, choice gjson.Result) bool {
		content := choice.Get("message.content").String()
		result += content + " "
		return true // 继续迭代
	})
	return result
}

package file_api

import (
	"ai_cr/vo"
	"testing"
)

func TestGetCompelteFunc(t *testing.T) {
	type args struct {
		fg string
	}
	tests := []struct {
		name    string
		args    args
		want    *vo.FuncSignature
		wantErr bool
	}{
		// 正常函数测试用例
		{
			name: "正常函数无参数无返回值",
			args: args{
				fg: "func SimpleFunc()",
			},
			want: &vo.FuncSignature{
				Name:       "SimpleFunc",
				Parameters: []string{},
				Results:    []string{},
			},
			wantErr: false,
		},
		{
			name: "正常函数有参数有返回值",
			args: args{
				fg: "func ProcessData(data string, count int) error",
			},
			want: &vo.FuncSignature{
				Name:       "ProcessData",
				Parameters: []string{"data string", "count int"},
				Results:    []string{"error"},
			},
			wantErr: false,
		},
		{
			name: "函数声明带有大括号",
			args: args{
				fg: "func HandleRequest(req *http.Request) ([]byte, error) {",
			},
			want: &vo.FuncSignature{
				Name:       "HandleRequest",
				Parameters: []string{"req http.Request"},
				Results:    []string{"[]byte", "error"},
			},
			wantErr: false,
		},

		// 方法（带接收者）测试用例
		{
			name: "方法带接收者变量",
			args: args{
				fg: "func (s *Service) GetUser(id string) (*User, error)",
			},
			want: &vo.FuncSignature{
				Name:       "GetUser",
				Receiver:   "*Service",
				Parameters: []string{"id string"},
				Results:    []string{"*User", "error"},
			},
			wantErr: false,
		},
		{
			name: "方法带指针接收者无变量名",
			args: args{
				fg: "func (*Repository) FindByID(id int) (*Entity, bool)",
			},
			want: &vo.FuncSignature{
				Name:       "FindByID",
				Receiver:   "*Repository",
				Parameters: []string{"id int"},
				Results:    []string{"*Entity", "bool"},
			},
			wantErr: false,
		},
		{
			name: "方法带值接收者",
			args: args{
				fg: "func (c Client) Close() error",
			},
			want: &vo.FuncSignature{
				Name:       "Close",
				Receiver:   "Client",
				Parameters: []string{},
				Results:    []string{"error"},
			},
			wantErr: false,
		},

		// 不完整函数签名测试用例
		{
			name: "不完整函数签名 - 缺少参数右括号",
			args: args{
				fg: "func ParseData(data string, options Options",
			},
			want: &vo.FuncSignature{
				Name:       "ParseData",
				Parameters: []string{},
				Results:    []string{},
			},
			wantErr: false,
		},
		{
			name: "不完整方法签名 - 参数列表不完整",
			args: args{
				fg: "func (p *Processor) Process(ctx context.Context, dat",
			},
			want: &vo.FuncSignature{
				Name:       "Process",
				Receiver:   "*Processor",
				Parameters: []string{},
				Results:    []string{},
			},
			wantErr: false,
		},
		{
			name: "不完整方法签名 - 只有函数名",
			args: args{
				fg: "func (handler *Handler) ServeHTTP",
			},
			want: &vo.FuncSignature{
				Name:       "ServeHTTP",
				Receiver:   "*Handler",
				Parameters: []string{},
				Results:    []string{},
			},
			wantErr: false,
		},

		// 复杂类型测试用例
		{
			name: "带泛型的函数",
			args: args{
				fg: "func ProcessItems[T any](items []T) []T",
			},
			want: &vo.FuncSignature{
				Name:       "ProcessItems",
				Parameters: []string{"items []T"},
				Results:    []string{"[]T"},
			},
			wantErr: false,
		},
		{
			name: "带函数类型参数的方法",
			args: args{
				fg: "func (router *Router) Use(middleware func(http.Handler) http.Handler)",
			},
			want: &vo.FuncSignature{
				Name:       "Use",
				Receiver:   "*Router",
				Parameters: []string{"middleware func(http.Handler) http.Handler"},
				Results:    []string{},
			},
			wantErr: false,
		},

		// 边缘情况测试用例
		{
			name: "空格过多的函数签名",
			args: args{
				fg: "func   Calculate  (  x   int ,  y  int  )   int  ",
			},
			want: &vo.FuncSignature{
				Name:       "Calculate",
				Parameters: []string{"x int", "y int"},
				Results:    []string{"int"},
			},
			wantErr: false,
		},
		{
			name: "接收者包含包限定符",
			args: args{
				fg: "func (c *pkg.Controller) HandleEvent(event pkg.Event)",
			},
			want: &vo.FuncSignature{
				Name:       "HandleEvent",
				Receiver:   "*pkg.Controller",
				Parameters: []string{"event pkg.Event"},
				Results:    []string{},
			},
			wantErr: false,
		},
		{
			name: "非函数声明",
			args: args{
				fg: "type User struct {",
			},
			want:    nil,
			wantErr: false, // GetCompelteFunc 返回 nil, nil 而不是错误
		},
		{
			name: "超级不完整的函数声明 - 只有 func 关键字",
			args: args{
				fg: "func ",
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetCompelteFunc(tt.args.fg)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCompelteFunc() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果期望返回nil结果，直接检查
			if tt.want == nil {
				if got != nil {
					t.Errorf("GetCompelteFunc() got = %v, want nil", got)
				}
				return
			}

			// 对于非nil结果，检查关键字段
			if got == nil {
				t.Errorf("GetCompelteFunc() unexpectedly returned nil")
				return
			}

			// 检查函数名
			if got.Name != tt.want.Name {
				t.Errorf("GetCompelteFunc().Name = %v, want %v", got.Name, tt.want.Name)
			}

			// 检查接收者类型
			if got.Receiver != tt.want.Receiver {
				t.Errorf("GetCompelteFunc().Receiver = %v, want %v", got.Receiver, tt.want.Receiver)
			}

			// 由于 ParseFuncSrc 不再保留参数和返回值信息，不检查这些字段
			// 在实际代码中，GetCompelteFunc 会返回来自生成的最小函数签名的空参数和结果
		})
	}
}

package file_api

import (
	"ai_cr/vo"
	"bytes"
	"encoding/base64"
	"errors"
	"fmt"
	"go/ast"
	"go/format"
	"go/parser"
	"go/printer"
	"go/token"
	"go/types"
	"log"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"

	"github.com/xanzy/go-gitlab"
)

func ParseSingleFile(file string, sourceCode []byte, info *types.Info, baseDir string) []*vo.FunctionInfo {
	// 解析源代码
	fset := token.NewFileSet()
	f, err := parser.ParseFile(fset, file, sourceCode, parser.ParseComments)
	if err != nil {
		log.Fatal(err)
	}
	fnList := make([]*vo.FunctionInfo, 0)
	// 遍历 AST
	ast.Inspect(f, func(node ast.Node) bool {
		// 示例：打印所有函数声明的名称
		if fn, ok := node.(*ast.FuncDecl); ok {
			// fmt.Printf("Function name: %s\n", fn.Name.Name)
			funcInfo := ExtractFunctionInfo(fn, fset, fn.Name.Name, info)
			if funcInfo != nil {
				// 把函数相对路径转为绝对路径
				// atFile := filepath.Join(filepath.Dir(file), funcInfo.AtFile)
				// pathInfo, err := GetAbsolutePathBasedOnDir(baseDir, funcInfo.AtFile)
				// if err != nil { return false }
				//fmt.Println(pathInfo + ":" + cast.ToString(funcInfo.PosStart))
				// signatureStr := funcInfo.String()
				// fmt.Printf("Function signature: %s\n", signatureStr)
				fnList = append(fnList, funcInfo)
			}
		}
		return true
	})
	return fnList
}

// ExtractFunctionInfo 提取函数ß信息
func ExtractFunctionInfo(fn *ast.FuncDecl, fset *token.FileSet, packageName string, info *types.Info) *vo.FunctionInfo {
	funcInfo := &vo.FunctionInfo{
		PackageName: packageName,
	}

	// 提取文档注释
	if fn.Doc != nil {
		funcInfo.Doc = fn.Doc.Text()
	}

	// 函数名
	funcInfo.Name = fn.Name.Name
	// 提取接收者
	if fn.Recv != nil && len(fn.Recv.List) > 0 {
		recvField := fn.Recv.List[0]

		// 获取接收者的名称
		var recvName string
		if len(recvField.Names) > 0 {
			recvName = recvField.Names[0].Name
		} else {
			recvName = ""
		}
		funcInfo.ReceiverName = recvName

		// 获取接收者的类型字符串表示
		var buf bytes.Buffer
		printer.Fprint(&buf, fset, recvField.Type)
		recvTypeStr := buf.String()
		funcInfo.ReceiverTypeStr = recvTypeStr

		// 获取接收者的类型信息
		recvType := info.TypeOf(recvField.Type)
		funcInfo.ReceiverType = recvType
	}

	// 提取参数列表
	funcInfo.Params = extractParams(fn.Type.Params, fset)

	// 提取返回值列表
	funcInfo.Results = extractParams(fn.Type.Results, fset)

	// 提取函数体
	if fn.Body != nil {
		var buf bytes.Buffer
		printer.Fprint(&buf, fset, fn.Body)
		funcInfo.Body = buf.String()
	}
	//  获取函数所在文件的相对路径
	funcInfo.AtFile = fset.File(fn.Pos()).Name()

	// 获取函数的位置信息
	funcInfo.PosStart = fset.Position(fn.Pos()).Line
	funcInfo.PosEnd = fset.Position(fn.End()).Line

	return funcInfo
}

// 提取参数列表或返回值列表
func extractParams(fieldList *ast.FieldList, fset *token.FileSet) []vo.ParamInfo {
	var params []vo.ParamInfo
	if fieldList == nil {
		return params
	}
	for _, field := range fieldList.List {
		// 获取参数类型
		var typeBuf bytes.Buffer
		printer.Fprint(&typeBuf, fset, field.Type)
		typeStr := typeBuf.String()

		// 参数名可能有多个
		if len(field.Names) > 0 {
			for _, name := range field.Names {
				params = append(params, vo.ParamInfo{
					Name: name.Name,
					Type: typeStr,
				})
			}
		} else {
			// 匿名参数
			params = append(params, vo.ParamInfo{
				Name: "",
				Type: typeStr,
			})
		}
	}
	return params
}

// GetBaseDir 返回当前源文件所在的目录
func GetBaseDir() (string, error) {
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		return "", fmt.Errorf("无法获取当前文件信息")
	}
	baseDir := filepath.Dir(filename)
	return baseDir, nil
}

// GetAbsolutePathBasedOnDir 将任意相对路径基于特定目录转换为绝对路径
func GetAbsolutePathBasedOnDir(baseDir, relativePath string) (string, error) {
	combinedPath := filepath.Join(baseDir, relativePath)
	absPath, err := filepath.Abs(combinedPath)
	if err != nil {
		return "", err
	}
	return absPath, nil
}

func GetFuncBody(list []*vo.FunctionInfo, signature *vo.FuncSignature) (string, error) {
	for _, fn := range list {
		if fn.Name == signature.Name && fn.ReceiverTypeStr == signature.Receiver {
			fnBody := fn.String() + fn.Body
			fmt.Println("GetFuncBody Success:" + fnBody)
			return fnBody, nil
		}
	}
	return "", fmt.Errorf("未找到函数 %s", signature.Name)
}

// FetchFileContent retrieves the content of a file from the repository
func FetchFileContent(git *gitlab.Client, projectID, branch, filePath string) (string, error) {
	file, _, err := git.RepositoryFiles.GetFile(projectID, filePath, &gitlab.GetFileOptions{Ref: gitlab.Ptr(branch)})
	if err != nil {
		return "", err
	}
	content := ""
	if file.Encoding == "base64" {
		// base64 decode the file content
		decodedContent, err := base64.StdEncoding.DecodeString(file.Content)
		if err != nil {
			return "", err
		}
		content = string(decodedContent)
	}
	// content := file.Encoding
	return content, nil
}

// ExtractFunction extracts and prints the function from the file content based on the given function name or start line
func ExtractFunction(content string, functionName string, startLine int) string {
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, "", content, parser.AllErrors)
	if err != nil {
		log.Printf("Error parsing file content: %v\n", err)
		return ""
	}

	var buf bytes.Buffer
	ast.Inspect(node, func(n ast.Node) bool {
		if fn, ok := n.(*ast.FuncDecl); ok {
			if (functionName != "" && fn.Name.Name == functionName) || (startLine > 0 && fset.Position(fn.Pos()).Line == startLine) {
				// Format the function body and write it to the buffer
				if err := format.Node(&buf, fset, fn); err != nil {
					log.Printf("Error formatting node: %v\n", err)
					return false
				}
				return false
			}
		}
		return true
	})

	return buf.String()
}
func isGoFuncFormat(s string) bool {
	// 去除字符串首尾空格
	s = strings.TrimSpace(s)
	// 定义匹配 Golang 函数定义的正则表达式
	pattern := `^func(\s*\([^\)]*\))?\s+\w+\s*\([^\)]*\)\s*\{}$`
	matched, err := regexp.MatchString(pattern, s)
	if err != nil {
		log.Printf("Error matching regex: %v\n", err)
		return false
	}
	return matched
}

func GetCompelteFunc(fg string) (*vo.FuncSignature, error) {
	// 如果开头不是`func`字符, 则返回为空
	if !strings.HasPrefix(strings.TrimSpace(fg), "func") {
		return nil, nil
	}
	// 1. 判断给定的字符串最后是否是`}`字符, 不是的话 则在最后拼接一个`}`符号
	// case1: func (wp *WrongProblemUsecase) GetUserPeriodWrongProblemList(ctx context.Context
	// case2: func (k *KafkaConsumer) Stop(ctx context.Context) error {
	// case3: func (k *KafkaConsumer) Stop(ctx context.Context) error
	// todo 异常case较多 需要进一步优化
	fg = ParseFuncSrc(fg)
	// 2. 判断整个字符串是否是一个golang函数格式 func (*) xxxx(*){}
	if !isGoFuncFormat(fg) {
		fmt.Println("invalid function format", fg)
		return nil, errors.New("invalid function format")
	}

	// 3. 如果是一个完整的函数则在上下文进行拼接`package main`
	completeFunc := "package main\n\n" + fg

	// 4. 解析ast 遍历 AST，查找函数声明节点
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, "", completeFunc, parser.AllErrors)
	if err != nil {
		log.Printf("Error parsing function:%s %v\n", fg, err)
		return nil, err
	}

	// 打印 AST 节点
	//ast.Print(fset, node)

	signature, done := GetFuncSign(node, fset)
	if done {
		return signature, nil
	}

	return nil, errors.New("no function signature found")

}

// ParseFuncSrc attempts to parse potentially incomplete function/method signatures
// from diff hunks into a minimal, valid Go function declaration string suitable for AST parsing.
// It handles missing parameters, return types, and body braces.
// Examples of expected input (from hunk section):
//
//	"func MyFunction(arg1 string, arg2 int) error {"
//	"func (r *MyReceiver) MyMethod(ctx context.Context)"
//	"func SimpleFunc()"
//	"func (t *Type) AnotherMethod("
//
// Output format: "func Name(){}" or "func (ReceiverType) MethodName(){}"
func ParseFuncSrc(funcSrc string) string {
	funcSrc = strings.TrimSpace(funcSrc)
	if !strings.HasPrefix(funcSrc, "func ") {
		return "" // Not a function/method signature start
	}

	// Remove the "func " prefix
	trimmed := strings.TrimPrefix(funcSrc, "func ")

	// Check if it's a method (starts with '(')
	if strings.HasPrefix(trimmed, "(") {
		// It's a method
		receiverEnd := strings.Index(trimmed, ")")
		if receiverEnd == -1 {
			// Malformed receiver, try to guess up to the first potential method name start
			firstSpaceAfterParen := strings.Index(trimmed[1:], " ")
			if firstSpaceAfterParen != -1 {
				receiverEnd = firstSpaceAfterParen + 1
			} else {
				return "" // Cannot determine receiver
			}
		}

		// Extract receiver part, including parentheses
		receiverPart := trimmed[:receiverEnd+1]
		// Extract the rest (potential method name and beyond)
		rest := strings.TrimSpace(trimmed[receiverEnd+1:])

		// Find method name (first word after receiver)
		methodName := rest
		paramStart := strings.Index(rest, "(")
		if paramStart != -1 {
			methodName = strings.TrimSpace(rest[:paramStart])
		} else {
			// If no '(', consider the first space as the end of the method name
			firstSpace := strings.Index(rest, " ")
			if firstSpace != -1 {
				methodName = rest[:firstSpace]
			}
			// If no space either, assume the whole remaining string is the name
		}

		// Clean up receiver type (remove variable name if present)
		receiverContent := strings.TrimSpace(receiverPart[1:receiverEnd]) // Content inside parens
		spaceIndex := strings.Index(receiverContent, " ")
		receiverType := receiverContent
		if spaceIndex != -1 {
			receiverType = strings.TrimSpace(receiverContent[spaceIndex+1:])
		}

		if methodName == "" {
			return "" // Could not extract method name
		}

		return fmt.Sprintf("func (%s) %s() {}", receiverType, methodName)

	} else {
		// It's a regular function
		funcName := trimmed
		paramStart := strings.Index(trimmed, "(")
		if paramStart != -1 {
			funcName = strings.TrimSpace(trimmed[:paramStart])
		} else {
			// If no '(', consider the first space as the end of the function name
			firstSpace := strings.Index(trimmed, " ")
			if firstSpace != -1 {
				funcName = trimmed[:firstSpace]
			}
			// If no space either, assume the whole remaining string is the name
		}

		if funcName == "" {
			return "" // Could not extract function name
		}

		return fmt.Sprintf("func %s() {}", funcName)
	}
}

func GetFuncSign(node *ast.File, fset *token.FileSet) (*vo.FuncSignature, bool) {
	funcSigs := &vo.FuncSignature{}
	for _, decl := range node.Decls {
		if fn, ok := decl.(*ast.FuncDecl); ok {
			// 提取函数名
			funcSigs.Name = fn.Name.Name

			// 提取接收者
			if fn.Recv != nil && len(fn.Recv.List) > 0 {
				recv := fn.Recv.List[0]
				var buf bytes.Buffer
				if err := printer.Fprint(&buf, fset, recv.Type); err != nil {
					log.Printf("Error printing receiver type: %v\n", err)
					return nil, true
				}
				funcSigs.Receiver = buf.String()
			}

			// 提取参数列表
			funcSigs.Parameters = extractFieldList(fset, fn.Type.Params)

			// 提取返回值列表
			funcSigs.Results = extractFieldList(fset, fn.Type.Results)

			// 假设只有一个函数，解析完毕后直接返回
			return funcSigs, true
		}
	}
	return nil, false
}

func extractFieldList(fset *token.FileSet, fieldList *ast.FieldList) []string {
	var fields []string
	if fieldList == nil {
		return fields
	}
	for _, field := range fieldList.List {
		var buf bytes.Buffer
		if err := printer.Fprint(&buf, fset, field.Type); err != nil {
			log.Printf("Error formatting field type: %v\n", err)
			continue
		}
		typeStr := buf.String()
		if len(field.Names) > 0 {
			for _, name := range field.Names {
				fields = append(fields, fmt.Sprintf("%s %s", name.Name, typeStr))
			}
		} else {
			fields = append(fields, typeStr)
		}
	}
	return fields
}

// IsRelevantGoFile checks if a file path corresponds to a Go source file that should be analyzed.
func IsRelevantGoFile(filePath string) bool {
	return strings.HasSuffix(filePath, ".go") &&
		!strings.HasSuffix(filePath, ".pb.go") &&
		!strings.HasSuffix(filePath, ".pb.validate.go") &&
		!strings.HasSuffix(filePath, "wire_gen.go")
}

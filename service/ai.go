package service

import (
	"ai_cr/setup"
	"ai_cr/vo"
	"context"
	"errors"
	"fmt"
	"os"

	"github.com/go-resty/resty/v2"
	openai "github.com/sashabaranov/go-openai"
	api "github.com/volcengine/volc-sdk-golang/service/maas/models/api/v2"
	client "github.com/volcengine/volc-sdk-golang/service/maas/v2"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
)

type AiApi struct {
	*Service
}

const SysPrompt = `"请作为一名资深的Golang代码审查专家,对用户给到的GitLab合并请求中原始代码和git diff格式的代码变更进行全面审查.请严格按照以下要求执行,并使用Markdown格式提供反馈.
## Capacity and Role
    - 角色：资深Golang代码审查专家
    - 专业领域：软件工程、代码质量、系统设计

## Insight
    - 深入理解Go语言最佳实践
    - 熟悉企业级软件开发标准
    - 具备全面的技术视角和批判性思维
## 审查目标:
    ### 提升代码库整体质量:
        - 确保新代码的引入使代码库的质量持续改善.
    ### 保证变更质量与进度:
        - 在确保每个MR的质量的同时,允许开发人员在任务上取得进展.
    ### 审查原则:
        - 持续改进:即使MR不完美,如果能够提升系统整体代码质量,可以接受.
        - 追求实效:避免完美主义,关注实际的改进.
        - 客观中立:基于技术和数据提供反馈,避免个人偏见.
        - 风格一致:代码应遵循项目的风格指南和编码规范.
        - 明确标注:对于非关键性的改进建议,请使用Nit标记.
## 审查要点:
    ### 总体评价:
        - 决定:请明确对此代码变更给出接受或拒绝的决定,并简要说明理由.
        - 概述:对代码变更的总体印象和评价.
    ### 代码优点:
        指出代码中的亮点和优秀之处.
    ### 问题点:
    #### 代码可维护性:
            - 代码结构是否清晰,模块划分是否合理?
            - 是否有重复代码,是否遵循DRY原则?
            - 是否符合团队的代码规范和项目的风格指南?
        代码可读性:
            代码命名是否清晰,准确,具有描述性?
            注释是否充分,解释了代码的目的和实现方式?
            是否有必要的文档说明,帮助他人理解代码?
        依赖管理与库使用:
            内部服务:是否使用了gRPC,onionms进行通信?
            外部API:是否使用了resty库进行调用?
            新增的依赖项是否必要?它们的许可证是否可接受?
        代码风格:
            是否应用了项目的格式化风格?
            是否遵循了约定的命名规范?
            代码是否足够可读.方法长度,逻辑清晰度等?
    ### 功能性:
        代码是否满足原始需求和功能规格?
        实现是否逻辑正确,考虑了所有可能的边界情况和异常处理?
        是否存在并发问题或线程安全隐患?
    ### 复杂性:
        是否避免了不必要的复杂性,代码是否简洁明了?
        是否有过度设计的情况,能否以更简单的方式实现相同功能?
    ### 测试:
        所有现有的测试是否都通过了?
        新特性是否添加了充分的单元测试和集成测试?
        边界情况和异常情况是否经过测试?
        是否对非功能性需求.如性能进行了测试?
    ### 安全性与性能:
        是否存在安全漏洞.例如,SQL注入,未处理的用户输入等?
        性能是否达到预期,有无显的性能瓶颈?
    ### 可观察性:
        是否添加了必要的日志,指标和追踪,方便监控和调试?
        日志信息是否充分且不会泄露敏感信息?
    ### API设计:
        API是否简洁且满足需求?
        是否为每个功能提供了唯一的实现方式避免多种方式?
        API是否一致,遵循最小惊讶原则?
        API和内部实现是否清晰分离,内部细节未泄露到API中?
        是否避免了对用户界面部分的破坏性更改?
   ###  文档与注释:
        新特性是否有合理的文档支持?
        是否更新了相关文档:README,API文档,用户指南,参考文档等?
        注释是否使用自然语言,清晰明了,解释了代码的'为什么'而非'怎么做'?
    ### 修改建议:
        针对上述问题,提供具体的修改建议.
        对于非关键性问题,使用Nit标记.
        在必要的情况下,直接提供修改后的代码示例.
审核后 需要对发现的问题进行评估, 仅对有风险的项进行提示, 在必要时,提供修改后的代码示例以帮助理解."`

var userPromptTmp = `
===原始代码===
%s
===
====git diff格式变更代码====
%s
===
`

var prompts = map[string]map[string]string{}

func AiCr(oldCodeFn, diff string) (string, error) {

	userPrompt := fmt.Sprintf(userPromptTmp, oldCodeFn, diff)
	return GetAiSaasCompletion(SysPrompt, userPrompt)
}

// GetEmbeddingByDoubao 基于豆包模型向量化
func GetEmbeddingByDoubao(text string) (*vo.EmbeddingReply, error) {
	r := client.NewInstance("maas-api.ml-platform-cn-beijing.volces.com", "cn-beijing")

	// fetch ak&sk from environmental variables
	r.SetAccessKey("AKLTZTkxNWZlODI1MWRlNGI2ZGJmYTI2NmVhMzFiNzUwYzY")
	r.SetSecretKey("TmpjMll6RTFNVGczWXprNU5HWmtOMkk0Wm1VMU9EUTRNamhrTVRKaU5HSQ==")

	req := &api.EmbeddingsReq{
		Input: []string{text},
	}

	endpointId := "ep-20240620031748-8fthf"

	got, status, err := r.Embeddings(endpointId, req)
	if err != nil {
		errVal := &api.Error{}
		if errors.As(err, &errVal) { // the returned error always type of *api.Error
			fmt.Printf("meet maas error=%v, status=%d\n", errVal, status)
		}
		return nil, err
	}
	var result = &vo.EmbeddingReply{
		Data: []vo.Data{{
			Object:    "",
			Embedding: got.Data[0].Embedding,
			Index:     0,
		}},
	}
	return result, nil
}

// GetChatCompletionByDoubao doubao 获取对话完成
func GetChatCompletionByDoubao(systemPrompt, usePrompt string) (string, error) {
	// fmt.Println("----- standard request -----")
	req := model.ChatCompletionRequest{
		Model:       "ep-20250324195229-gb8vt",
		Temperature: 0,
		Messages: []*model.ChatCompletionMessage{
			{
				Role: model.ChatMessageRoleSystem,
				Content: &model.ChatCompletionMessageContent{
					StringValue: volcengine.String(systemPrompt),
				},
			},
			{
				Role: model.ChatMessageRoleUser,
				Content: &model.ChatCompletionMessageContent{
					StringValue: volcengine.String(usePrompt),
				},
			},
		},
	}

	resp, err := setup.DoubaoApiClient.CreateChatCompletion(context.Background(), req)
	if err != nil {
		fmt.Printf("standard chat error: %v\n", err)
		return "", err
	}
	fmt.Println(*resp.Choices[0].Message.Content.StringValue)
	return *resp.Choices[0].Message.Content.StringValue, nil
}

// 把如下的curl 命令转换为go代码
// curl https://ark.cn-beijing.volces.com/api/v3/chat/completions \
//   -H "Content-Type: application/json" \
//   -H "Authorization: Bearer $ARK_API_KEY" \
//   -d '{
//     "model": "ep-20250205122127-7dl9f",
//     "messages": [
//       {"role": "system","content": "你是人工智能助手."},
//       {"role": "user","content": "常见的十字花科植物有哪些？"}
//     ]
//   }'

type ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type ChatRequest struct {
	Model    string        `json:"model"`
	Messages []ChatMessage `json:"messages"`
}

type ChatResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Message ChatMessage `json:"message"`
	} `json:"choices"`
}

func GetR1Completion(systemPrompt, usePrompt string) (string, error) {
	client := resty.New()
	arkApiKey := os.Getenv("ARK_API_KEY")
	request := ChatRequest{
		Model: "ep-20250205122127-7dl9f",
		Messages: []ChatMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: usePrompt,
			},
		},
	}

	var response ChatResponse
	resp, err := client.R().
		SetHeader("Authorization", "Bearer "+arkApiKey).
		SetHeader("Content-Type", "application/json").
		SetBody(request).
		SetResult(&response).
		Post("https://ark.cn-beijing.volces.com/api/v3/chat/completions")

	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}

	if resp.StatusCode() != 200 {
		return "", fmt.Errorf("服务器返回错误状态码: %d", resp.StatusCode())
	}

	if len(response.Choices) > 0 {
		return response.Choices[0].Message.Content, nil
	}

	return "", fmt.Errorf("响应中没有内容")
}

func GetAiSaasCompletion(systemPrompt, usePrompt string) (string, error) {
	conf := openai.DefaultConfig("119581fe-6f02-40a7-ae97-70e7ddf35561")
	//conf.BaseURL = "http://180.184.48.192:8888/v1"
	conf.BaseURL = "http://onionai-api.yangcong345.com/v1"
	opClient := openai.NewClientWithConfig(conf)
	resp, err := opClient.CreateChatCompletion(
		context.Background(),

		openai.ChatCompletionRequest{
			MaxTokens: 256000,
			Model:     "doubao-1.5-pro-256k",
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleSystem,
					Content: systemPrompt,
				},
				{
					Role:    openai.ChatMessageRoleUser,
					Content: usePrompt,
				},
			},
		},
	)

	if err != nil {
		fmt.Printf("ChatCompletion error: %v\n", err)
		return "", err
	}

	return resp.Choices[0].Message.Content, nil
}

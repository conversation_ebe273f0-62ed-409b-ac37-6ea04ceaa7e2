package service

import (
	"context"
	"encoding/json"
	"fmt"
	"go/ast"
	"go/types"
	"log"

	"ai_cr/dao"
	file_api "ai_cr/service/file_repo"
	"ai_cr/setup"
	"ai_cr/vo"

	sg_diff "github.com/sourcegraph/go-diff/diff"
	"github.com/spf13/cast"
	"github.com/xanzy/go-gitlab"
)

type MrApi struct {
	*Service
}

// findOrCreateProject 查找或创建一个项目
// 参数:
//   - gitlabProjectID: GitLab项目ID
//   - projectName: 项目名称
//
// 返回:
//   - *dao.Project: 项目对象
//   - error: 错误信息
func (p *MrA<PERSON>) findOrCreateProject(gitlabProjectID string, projectName string) (*dao.Project, error) {
	var project dao.Project
	result := setup.AiProblemPgConn.Where("gitlab_id = ?", gitlabProjectID).First(&project)

	if result.Error != nil {
		// 如果项目不存在，创建新项目
		project = dao.Project{
			GitlabID: cast.ToInt(gitlabProjectID),
			Name:     projectName,
		}
		result = setup.AiProblemPgConn.Create(&project)
		if result.Error != nil {
			return nil, result.Error
		}
	}

	return &project, nil
}

// findOrCreateRepository 查找或创建一个代码仓库
// 参数:
//   - projectID: 项目ID
//   - repoName: 仓库名称
//
// 返回:
//   - *dao.Repository: 仓库对象
//   - error: 错误信息
func (p *MrApi) findOrCreateRepository(projectID uint, repoName string) (*dao.Repository, error) {
	var repository dao.Repository
	result := setup.AiProblemPgConn.Where("project_id = ? AND name = ?", projectID, repoName).First(&repository)

	if result.Error != nil {
		// 如果仓库不存在，创建新仓库
		repository = dao.Repository{
			ProjectID: projectID,
			Name:      repoName,
		}
		result = setup.AiProblemPgConn.Create(&repository)
		if result.Error != nil {
			return nil, result.Error
		}
	}

	return &repository, nil
}

// findOrCreateBranch 查找或创建一个分支
// 参数:
//   - repoID: 仓库ID
//   - branchName: 分支名称
//   - commitSHA: 提交的SHA值
//
// 返回:
//   - *dao.Branch: 分支对象
//   - error: 错误信息
func (p *MrApi) findOrCreateBranch(repoID uint, branchName string, commitSHA string) (*dao.Branch, error) {
	var branch dao.Branch
	result := setup.AiProblemPgConn.Where("repository_id = ? AND name = ?", repoID, branchName).First(&branch)

	if result.Error != nil {
		// 如果分支不存在，创建新分支
		branch = dao.Branch{
			RepositoryID: repoID,
			Name:         branchName,
			CommitSHA:    commitSHA,
		}
		result = setup.AiProblemPgConn.Create(&branch)
		if result.Error != nil {
			return nil, result.Error
		}
	}

	return &branch, nil
}

// MrProcess 处理合并请求的主要函数
// 参数:
//   - ctx: 上下文对象
//   - pID: 项目ID (字符串形式)
//   - mrIID: 合并请求的内部ID
func (p *MrApi) MrProcess(ctx context.Context, gitClient *gitlab.Client, pID string, mrIID int) {
	// 1. 读取当前次的变动
	mrInfo, mergeDiffs, err := p.fetchMrChanges(ctx, gitClient, pID, mrIID)
	if err != nil {
		log.Printf("Error fetching MR changes for pID %s, mrIID %d: %v", pID, mrIID, err)
		return // 或者根据需要处理错误，例如返回错误信息
	}

	// 初始化类型信息收集器
	info := &types.Info{
		Types:      make(map[ast.Expr]types.TypeAndValue),
		Defs:       make(map[*ast.Ident]types.Object),
		Uses:       make(map[*ast.Ident]types.Object),
		Scopes:     make(map[ast.Node]*types.Scope),
		Selections: make(map[*ast.SelectorExpr]*types.Selection),
	}

	// 2. 基于当前次的变动提取上下文并执行AI评估
	p.processChangesForEvaluation(ctx, gitClient, mrInfo, mergeDiffs, info)
}

// fetchMrChanges 获取合并请求的基本信息和文件差异
func (p *MrApi) fetchMrChanges(ctx context.Context, gitClient *gitlab.Client, pID string, mrIID int) (*vo.MrInfo, []*vo.MergeDiff, error) {
	mr, _, err := gitClient.MergeRequests.GetMergeRequest(pID, mrIID, &gitlab.GetMergeRequestsOptions{})
	if err != nil {
		return nil, nil, fmt.Errorf("fetching MR details: %w", err)
	}

	mrInfo := &vo.MrInfo{
		ProjectID: cast.ToString(mr.ProjectID),
		BaseSHA:   mr.DiffRefs.BaseSha,
		StartSHA:  mr.DiffRefs.StartSha,
		HeadSHA:   mr.DiffRefs.HeadSha,
		MrIID:     mrIID,
	}

	fmt.Printf("BaseSHA: %s\n", mrInfo.BaseSHA)
	fmt.Printf("StartSHA: %s\n", mrInfo.StartSHA)
	fmt.Printf("HeadSHA: %s\n", mrInfo.HeadSHA)

	mergeDiffs := make([]*vo.MergeDiff, 0)
	page := 1
	perPage := 20

	for {
		mrDiffsResp, response, err := gitClient.MergeRequests.ListMergeRequestDiffs(mrInfo.ProjectID, mrIID, &gitlab.ListMergeRequestDiffsOptions{
			ListOptions: gitlab.ListOptions{
				Page:    page,
				PerPage: perPage,
			},
		})
		if err != nil {
			// 不直接Fatal，返回错误让调用者处理
			return mrInfo, nil, fmt.Errorf("fetching MR diffs page %d: %w", page, err)
		}

		for _, diff := range mrDiffsResp {
			// 过滤非Go源文件
			if !file_api.IsRelevantGoFile(diff.NewPath) {
				continue
			}

			parsedHunks, err := sg_diff.ParseHunks([]byte(diff.Diff))
			if err != nil {
				// 记录错误并跳过这个diff，而不是Fatal
				log.Printf("Error parsing hunks for file %s: %v", diff.NewPath, err)
				continue
			}

			diffChunks := make([]*vo.DiffChunk, 0, len(parsedHunks))
			for _, hunk := range parsedHunks {
				dc := &vo.DiffChunk{
					Hunk: hunk,
				}
				dc.Changes = dc.Detail()
				diffChunks = append(diffChunks, dc)
			}

			mergeDiffs = append(mergeDiffs, &vo.MergeDiff{
				MergeRequestDiff: diff,
				DiffChunks:       diffChunks,
			})
		}

		if response.CurrentPage >= response.TotalPages {
			break
		}
		page++
	}

	return mrInfo, mergeDiffs, nil
}

// processChangesForEvaluation 处理变更，提取上下文并执行AI评估
func (p *MrApi) processChangesForEvaluation(ctx context.Context, gitClient *gitlab.Client, mrInfo *vo.MrInfo, mergeDiffs []*vo.MergeDiff, typeInfo *types.Info) {
	for _, diff := range mergeDiffs {
		// 文件过滤 (虽然在fetch时已过滤，这里可以保留以防万一)
		if !file_api.IsRelevantGoFile(diff.NewPath) {
			continue
		}

		// Assign unused return values to the blank identifier
		_, _, oldFnList, err := p.extractChangeContext(ctx, gitClient, mrInfo, diff, typeInfo)
		if err != nil {
			log.Printf("Error extracting context for file %s: %v", diff.NewPath, err)
			continue // 跳过处理这个文件
		}

		if diff.DiffChunks != nil {
			path := diff.NewPath
			for _, dc := range diff.DiffChunks {
				// 只有当 hunk 中有添加的代码时才进行评估
				if dc.Hunk.Stat().Added <= 0 {
					continue
				}

				fnBody := ""
				diffContext := vo.ChunkToString(&vo.MergeDiff{DiffChunks: []*vo.DiffChunk{dc}}) // 默认使用hunk diff

				// 尝试获取函数体作为更精确的上下文
				if dc.Hunk.Section != "" {
					fnSignature, sigErr := file_api.GetCompelteFunc(dc.Hunk.Section)
					if sigErr != nil {
						log.Printf("Error getting function signature from hunk section for %s: %v", path, sigErr)
						// 获取签名失败，继续使用 hunk diff 作为上下文
					} else if fnSignature != nil {
						extractedBody, bodyErr := file_api.GetFuncBody(oldFnList, fnSignature)
						if bodyErr != nil {
							log.Printf("Error extracting function body for %s, signature %v: %v", path, fnSignature, bodyErr)
							// 获取函数体失败，继续使用 hunk diff 作为上下文
						} else if extractedBody != "" {
							fnBody = extractedBody // 成功获取到函数体，用它作为 oldCode
							log.Printf("Successfully extracted function body for AI CR on %s", path)
						}
					}
				}

				var oldCodeForAI string
				if fnBody != "" {
					oldCodeForAI = fnBody // 优先使用函数体
				} else {
					oldCodeForAI = "" // 如果没有函数体，oldCode 为空，让AI仅基于diff评估
					log.Printf("No function body found for hunk in %s, using diff only for AI CR.", path)
				}

				// 执行AI评估
				evaluation := p.AiCr(oldCodeForAI, diffContext)
				if evaluation != "" {
					// 添加评论
					p.ReplyForCr(path, evaluation, mrInfo.BaseSHA, mrInfo.HeadSHA, mrInfo.StartSHA, dc, gitClient, mrInfo.ProjectID, mrInfo.MrIID)
				}
			}
		}
	}

	// 发布所有的MR草稿笔记
	p.********************(mrInfo.ProjectID, mrInfo.MrIID, gitClient)
}

// extractChangeContext 提取单个文件变更的上下文信息（新旧文件内容和旧版本函数列表）
func (p *MrApi) extractChangeContext(ctx context.Context, gitClient *gitlab.Client, mrInfo *vo.MrInfo, diff *vo.MergeDiff, typeInfo *types.Info) (oldCode string, newCode string, oldFnList []*vo.FunctionInfo, err error) {
	// 获取新版本文件内容
	newCode, err = file_api.FetchFileContent(gitClient, mrInfo.ProjectID, mrInfo.HeadSHA, diff.NewPath)
	if err != nil {
		return "", "", nil, fmt.Errorf("fetching new file content %s@%s: %w", diff.NewPath, mrInfo.HeadSHA, err)
	}
	// 解析新文件（虽然结果暂时未使用，但保留解析过程）
	_ = file_api.ParseSingleFile(diff.NewPath, []byte(newCode), typeInfo, "")

	// 获取旧版本文件内容
	// 对于新创建的文件，startSHA 可能无效或文件不存在，需要处理错误
	oldCode, err = file_api.FetchFileContent(gitClient, mrInfo.ProjectID, mrInfo.StartSHA, diff.OldPath) // 使用 OldPath 获取旧文件
	if err != nil {
		// 如果获取旧文件出错 (例如文件是新增的)，将 oldCode 设为空字符串，并记录日志，但不认为是致命错误
		log.Printf("Could not fetch old file content %s@%s (maybe new file?): %v", diff.OldPath, mrInfo.StartSHA, err)
		oldCode = ""
		err = nil // 清除错误，因为这是预期的场景
	}

	// 只有在旧文件内容存在时才解析旧文件的函数列表
	if oldCode != "" {
		oldFnList = file_api.ParseSingleFile(diff.OldPath, []byte(oldCode), typeInfo, "")
	} else {
		oldFnList = make([]*vo.FunctionInfo, 0) // 初始化为空列表
	}

	return oldCode, newCode, oldFnList, nil
}

// ReplyForCr 在代码审查中添加AI评估回复
// 参数:
//   - path: 文件路径
//   - evaluation: AI评估结果
//   - baseSHA: 基础提交SHA
//   - headSHA: 目标提交SHA
//   - startSHA: 起始提交SHA
//   - dc: 差异块
//   - git: GitLab客户端
//   - projectID: 项目ID
//   - mrIID: 合并请求的内部ID
func (p *MrApi) ReplyForCr(path string, evaluation string, baseSHA string, headSHA string, startSHA string, dc *vo.DiffChunk, git *gitlab.Client, projectID string, mrIID int) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Recovered in ReplyForCr: %v", r)
		}
	}()

	t := fmt.Sprintf("AI自动审核如下: %s: %s", path, evaluation)

	ty := "text"
	pos := &gitlab.PositionOptions{
		StartSHA:     gitlab.Ptr(baseSHA),
		HeadSHA:      gitlab.Ptr(headSHA),
		BaseSHA:      gitlab.Ptr(startSHA),
		PositionType: gitlab.Ptr(ty),
		NewPath:      gitlab.Ptr(path),
		NewLine:      gitlab.Ptr(dc.GetFirstAdd()),
	}

	// 创建草稿评论
	dNote, _, err := git.DraftNotes.CreateDraftNote(projectID, mrIID, &gitlab.CreateDraftNoteOptions{
		Note:     gitlab.Ptr(t),
		Position: pos,
	})
	if err != nil {
		log.Printf("Error creating draft note for %s: %v", path, err)
		return // 创建失败则不继续发布
	}

	dNoteJSON, _ := json.Marshal(dNote) // 忽略错误，仅用于日志
	log.Printf("Created draft note: %s", string(dNoteJSON))

	// 发布草稿评论 - PublishDraftNote returns 2 values: *Note, *Response, error
	// We only need the error here, the published note object isn't used directly after logging.
	// resp, err := git.DraftNotes.PublishDraftNote(projectID, mrIID, dNote.ID)
	// if err != nil {
	// 	// 如果发布失败，尝试删除草稿，避免累积
	// 	log.Printf("Error publishing draft note %d for %s: %v. Response: %v. Attempting to delete draft.", dNote.ID, path, err, resp)
	// 	_, delErr := git.DraftNotes.DeleteDraftNote(projectID, mrIID, dNote.ID)
	// 	if delErr != nil {
	// 		log.Printf("Error deleting draft note %d after publish failure: %v", dNote.ID, delErr)
	// 	}
	// 	return
	// }

	// // Log success, note object isn't needed further
	// log.Printf("Successfully published note for %s. Response: %v", path, resp)
}

// 批量发布所有的MR草稿笔记
func (p *MrApi) ********************(projectId string, mrIID int, git *gitlab.Client) {
	res, err := git.DraftNotes.********************(projectId, mrIID)
	if err != nil {
		fmt.Printf("Error ******************** MR draft note: %v\n", err)
	}
	tres, err := json.Marshal(res)
	fmt.Println(tres)
}

// AiCr 执行AI代码审查
// 参数:
//   - oldCode: 原始代码
//   - diffCode: 差异代码
//
// 返回:
//   - string: AI评估结果
func (p *MrApi) AiCr(oldCode, diffCode string) string {
	res, err := AiCr(oldCode, diffCode)
	if err != nil {
		fmt.Println("Error in AI CR:", err)
		return ""
	}
	return res
}

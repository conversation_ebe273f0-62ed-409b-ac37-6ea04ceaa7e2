# AI Code Review

AI Code Review 是一个基于 AI 的自动化代码审查系统，专门用于对 GitLab 合并请求（Merge Request）中的代码变更进行智能分析和审查。系统利用火山引擎的 AI 服务，提供专业的 Golang 代码审查建议。

## 功能特点

- 自动化代码审查：对 GitLab MR 中的代码变更进行自动分析
- AI 驱动：利用火山引擎 AI 模型进行代码质量评估
- 全面的审查维度：包括代码质量、可维护性、性能、安全性等
- RESTful API：基于 Gin 框架提供 HTTP API 服务
- 数据持久化：使用 PostgreSQL 数据库存储相关数据

## 系统要求

- Go 1.21.5 或更高版本
- PostgreSQL 数据库
- 火山引擎 API 密钥
- GitLab 访问令牌

## 安装

1. 克隆项目：
```bash
git clone <repository_url>
cd ai_cr
```

2. 安装依赖：
```bash
go mod download
```

3. 配置环境：
   - 复制 `conf/default.yaml` 为你的环境配置文件
   - 配置数据库连接信息
   - 设置火山引擎 API 密钥
   - 配置 GitLab 访问令牌

4. 启动服务：
```bash
go run main.go
```

服务默认在 `0.0.0.0:8000` 端口启动，可通过配置文件修改。

## API 使用

基础 API 路径：`/ai_cr`

### 示例请求

```bash
curl -X POST http://localhost:8000/ai_cr/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "merge_request_id": "123",
    "project_id": "456"
  }'
```


## 在GitLab中配置Webhook的流程如下：

1. **登录GitLab项目**
   - 进入您需要配置Webhook的GitLab项目

2. **进入项目设置**
   - 点击左侧菜单栏的"设置"(Settings)
   - 选择"Webhooks"或"集成"(Integrations)

3. **添加新的Webhook**
   - 点击"添加Webhook"(Add Webhook)按钮

4. **配置Webhook参数**

   **基本参数**:
   - URL: `https://aicr.yc345.tv/ai_cr/mr/diff`
   - 触发器选择:
     - Merge request events (合并请求事件)

   **高级设置**:
   - SSL验证: 请启用
   - 启用Webhook: 确保勾选

5. **添加Webhook**
   - 点击"添加Webhook"(Add Webhook)按钮保存配置

6. **测试Webhook**
   - 在创建后的Webhook列表中，可以找到"Test"按钮
   - 选择需要测试的事件类型进行测试
   - 检查日志确认请求是否被正确接收

**注意事项**:
- 确保您的服务器能够被GitLab服务器访问
- 您的服务需要正确处理GitLab的Webhook请求，根据您代码中的`pr.POST("/diff", mr.GitlabHook)`路由配置
- 确保Webhook URL以`/ai_cr/diff`结尾，与您的应用路由匹配
- 安全令牌(Secret Token)需要在您的应用中进行验证，以确保请求来源可靠


## 配置说明

主要配置项在 `conf/default.yaml` 中：

- 数据库配置
- 火山引擎 AI 服务配置
- GitLab 配置
- 日志配置

## 开发

项目结构：
```
.
├── cmd/                    # 命令行工具
├── conf/                   # 配置文件
│   ├── ai_config.go       # AI 服务相关配置
│   ├── conf.go            # 配置管理核心逻辑
│   ├── default.yaml       # 默认配置文件
│   └── test.yaml          # 测试环境配置文件
├── dao/                   # 数据访问层
│   └── project.go         # 项目、仓库、分支、代码文件等数据模型和数据库操作
├── router/                # 路由定义
│   ├── mr.go             # 合并请求相关的 HTTP 接口处理
│   └── router.go         # 路由初始化和注册
├── service/              # 业务逻辑层
│   ├── ai.go            # AI 服务封装，包括火山引擎 AI 接口调用
│   ├── mr.go            # 合并请求处理核心逻辑
│   └── service.go       # 服务层基础结构
├── setup/               # 初始化配置
│   ├── doubaoApiClient.go    # 豆包 API 客户端初始化
│   ├── gitlab_client.go      # GitLab 客户端初始化
│   ├── logger.go            # 日志配置初始化
│   └── pgsql.go            # PostgreSQL 数据库连接初始化
├── utils/              # 工具函数
│   └── tools.go       # 通用工具函数
└── vo/                # 数据模型
    ├── embed.go       # 嵌入向量相关模型
    └── vo.go         # 通用数据模型定义
```

### 核心模块说明

#### conf - 配置管理
- `ai_config.go`: 定义 AI 服务配置结构和管理方法
- `conf.go`: 实现配置文件加载、解析和访问的核心功能

#### dao - 数据访问层
- `project.go`: 定义项目相关的数据模型（Project、Repository、Branch、CodeFile）及其数据库操作

#### router - 路由层
- `mr.go`: 实现合并请求相关的 HTTP 接口，包括代码审查和测试用例接口
- `router.go`: 配置和初始化所有 API 路由

#### service - 业务逻辑层
- `ai.go`: 封装 AI 服务调用，包括：
  - 代码审查（AiCr）
  - 文本向量化（GetEmbeddingByDoubao）
  - AI 对话完成（GetChatCompletionByDoubao）
- `mr.go`: 实现合并请求处理的核心业务逻辑，包括：
  - 项目、仓库、分支的管理
  - 代码审查流程处理
  - 审查结果回复

#### setup - 初始化模块
- 负责各个外部服务和组件的初始化：
  - 豆包 API 客户端
  - GitLab 客户端
  - 日志系统
  - PostgreSQL 数据库连接

### 主要流程

1. 系统启动时通过 `main.go` 初始化各个组件
2. 接收到合并请求后，通过路由层转发到相应的处理函数
3. 服务层处理合并请求：
   - 获取代码变更信息
   - 调用 AI 服务进行代码审查
   - 将审查结果保存并回复到 GitLab
4. 数据访问层负责持久化必要的信息


## 贡献

欢迎提交 Pull Request 或创建 Issue 来帮助改进项目。

## 许可证

[License Name] - 详见 LICENSE 文件

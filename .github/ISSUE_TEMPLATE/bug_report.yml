name: Bug Report
description: You think something is broken
title: "[Bug]: "
labels: ["bug", "triage"]

body:
  - type: markdown
    attributes:
      value: |
        > The title of the bug report should be short and descriptive.
        > Use relevant keywords for searchability.
        > Do not leave it blank, but also do not put an entire error log in it.
  - type: checkboxes
    attributes:
      label: Checklist
      description: |
        Please perform basic debugging to see if your configuration is the cause of the issue.
        Basic debug procedure
        　1. Update - sometimes things just need to be updated
        　2. Backup and revert your .env adjustments - check if the issue is caused by bad configuration
        　3. Try a fresh installation in a different directory - see if a clean installation solves the issue
        Before making a issue report please, check that the issue hasn't been reported recently.
      options:
        - label: The issue exists on a clean installation
        - label: The issue exists in the current version
        - label: The issue has not been reported before
        - label: The issue has been reported before but has not been fixed yet
  - type: markdown
    attributes:
      value: |
        > Please fill this form with as much information as possible. Don't forget to add information about "Which OS" and provide screenshots if possible
  - type: textarea
    id: what-did
    attributes:
      label: What happened?
      description: Tell us what happened in a very clear and simple way
      placeholder: |
        application crashes on bootup.
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce the problem
      description: Please provide us with precise step by step instructions on how to reproduce the bug
      placeholder: |
        1. Go to ...
        2. Press ...
        3. ...
    validations:
      required: true
  - type: textarea
    id: what-should
    attributes:
      label: What should have happened?
      description: Tell us what you think the normal behavior should be
      placeholder: |
        It should have ...
    validations:
      required: true
  - type: dropdown
    id: browsers
    attributes:
      label: What browsers do you use to access the dashboard?
      multiple: true
      options:
        - Mozilla Firefox
        - Google Chrome
        - Brave
        - Apple Safari
        - Microsoft Edge
        - Android
        - iOS
        - Other
  - type: dropdown
    id: hosting
    attributes:
      label: Where are you running the app?
      multiple: false
      options:
        - Locally
        - Locally with virtualization (e.g. Docker)
        - Cloud (e.g. AWS, GCP, Azure)
  - type: input
    id: operating-system
    attributes:
      label: What operating system are you using?
      placeholder: |
        Windows 11 / MacOS 15.3.2
  - type: textarea
    id: logs
    attributes:
      label: Console logs
      description: Please provide **full** cmd/terminal logs from the moment you started UI to the end of it, after the bug occurred. If it's very long, provide a link to pastebin or similar service.
      render: Shell
    validations:
      required: true
  - type: textarea
    id: misc
    attributes:
      label: Additional information
      description: | 
        Please provide us with any relevant additional info or context.
        Examples:
        　I have updated packages recently.
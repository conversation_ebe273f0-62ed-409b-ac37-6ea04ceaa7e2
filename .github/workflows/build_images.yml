name: Docker image build

on:
  push:
    branches:
      - main
    tags:
      - v*

jobs:
  build-and-push-images:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU for multi-arch builds
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Extract tags with suffixes for app and worker
      - name: Extract metadata (tags, labels) for Docker (app)
        id: meta_app
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository_owner }}/${{ github.event.repository.name }}
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=edge,branch=main

      - name: Extract metadata (tags, labels) for Docker (worker)
        id: meta_worker
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository_owner }}/${{ github.event.repository.name }}
          tags: |
            type=semver,pattern={{version}},suffix=-worker
            type=semver,pattern={{major}}.{{minor}},suffix=-worker
            type=semver,pattern={{major}},suffix=-worker
            type=edge,branch=main,suffix=-worker

      # Build and push multi-arch app image
      - name: Build and push app Docker image (Multi-Arch)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          platforms: linux/amd64,linux/arm64/v8
          tags: ${{ steps.meta_app.outputs.tags }}
          labels: ${{ steps.meta_app.outputs.labels }}
          target: app

      # Build and push multi-arch worker image
      - name: Build and push worker Docker image (Multi-Arch)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          platforms: linux/amd64,linux/arm64/v8
          tags: ${{ steps.meta_worker.outputs.tags }}
          labels: ${{ steps.meta_worker.outputs.labels }}
          target: worker
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1806pt" height="8397pt" viewBox="0.00 0.00 1806.00 8396.98">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 8392.98)">
<title>G</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-8392.98 1802,-8392.98 1802,4 -4,4"/>
<!-- start -->
<g id="node1" class="node">
<title>start</title>
<ellipse fill="none" stroke="black" cx="1349" cy="-8370.98" rx="69.24" ry="18"/>
<text text-anchor="middle" x="1349" y="-8366.78" font-family="Times,serif" font-size="14.00">开始 MrProcess</text>
</g>
<!-- fetch_mr_details -->
<g id="node2" class="node">
<title>fetch_mr_details</title>
<ellipse fill="none" stroke="black" cx="1349" cy="-8167.68" rx="240.07" ry="41.3"/>
<text text-anchor="middle" x="1349" y="-8180.28" font-family="Times,serif" font-size="14.00">调用 GitLab API 获取合并请求(MR)详情 (GetMergeRequest)</text>
<text text-anchor="middle" x="1349" y="-8163.48" font-family="Times,serif" font-size="14.00">- 输入: 项目ID (pID), MR内部ID (mrIID)</text>
<text text-anchor="middle" x="1349" y="-8146.68" font-family="Times,serif" font-size="14.00">- 输出: MR对象 (mr), 错误 (err)</text>
</g>
<!-- start&#45;&gt;fetch_mr_details -->
<g id="edge1" class="edge">
<title>start-&gt;fetch_mr_details</title>
<path fill="none" stroke="black" d="M1349,-8352.82C1349,-8323.84 1349,-8264.12 1349,-8220.52"/>
<polygon fill="black" stroke="black" points="1352.5,-8220.77 1349,-8210.77 1345.5,-8220.77 1352.5,-8220.77"/>
</g>
<!-- check_mr_error -->
<g id="node3" class="node">
<title>check_mr_error</title>
<ellipse fill="none" stroke="black" cx="1349" cy="-7964.39" rx="102.78" ry="18"/>
<text text-anchor="middle" x="1349" y="-7960.19" font-family="Times,serif" font-size="14.00">检查获取MR详情是否出错?</text>
</g>
<!-- fetch_mr_details&#45;&gt;check_mr_error -->
<g id="edge2" class="edge">
<title>fetch_mr_details-&gt;check_mr_error</title>
<path fill="none" stroke="black" d="M1349,-8125.95C1349,-8086.69 1349,-8028.32 1349,-7993.83"/>
<polygon fill="black" stroke="black" points="1352.5,-7994.21 1349,-7984.21 1345.5,-7994.21 1352.5,-7994.21"/>
</g>
<!-- log_mr_error -->
<g id="node4" class="node">
<title>log_mr_error</title>
<ellipse fill="none" stroke="black" cx="1228" cy="-7737.33" rx="61.74" ry="18"/>
<text text-anchor="middle" x="1228" y="-7733.13" font-family="Times,serif" font-size="14.00">记录错误并退出</text>
</g>
<!-- check_mr_error&#45;&gt;log_mr_error -->
<g id="edge3" class="edge">
<title>check_mr_error-&gt;log_mr_error</title>
<path fill="none" stroke="black" d="M1339.71,-7946.1C1318.76,-7907.14 1267.73,-7812.23 1242.55,-7765.39"/>
<polygon fill="black" stroke="black" points="1245.69,-7763.85 1237.88,-7756.7 1239.53,-7767.17 1245.69,-7763.85"/>
</g>
<!-- extract_mr_info -->
<g id="node5" class="node">
<title>extract_mr_info</title>
<ellipse fill="none" stroke="black" cx="1470" cy="-7737.33" rx="107.81" ry="65.06"/>
<text text-anchor="middle" x="1470" y="-7766.73" font-family="Times,serif" font-size="14.00">提取 MR 信息:</text>
<text text-anchor="middle" x="1470" y="-7749.93" font-family="Times,serif" font-size="14.00">- 项目ID (projectID)</text>
<text text-anchor="middle" x="1470" y="-7733.13" font-family="Times,serif" font-size="14.00">- Base SHA (baseSHA)</text>
<text text-anchor="middle" x="1470" y="-7716.33" font-family="Times,serif" font-size="14.00">- Start SHA (startSHA)</text>
<text text-anchor="middle" x="1470" y="-7699.53" font-family="Times,serif" font-size="14.00">- Head SHA (headSHA)</text>
</g>
<!-- check_mr_error&#45;&gt;extract_mr_info -->
<g id="edge4" class="edge">
<title>check_mr_error-&gt;extract_mr_info</title>
<path fill="none" stroke="black" d="M1358.29,-7946.1C1373.84,-7917.18 1405.98,-7857.42 1431.8,-7809.38"/>
<polygon fill="black" stroke="black" points="1434.84,-7811.12 1436.49,-7800.66 1428.67,-7807.81 1434.84,-7811.12"/>
</g>
<!-- fetch_project_details -->
<g id="node6" class="node">
<title>fetch_project_details</title>
<ellipse fill="none" stroke="black" cx="1470" cy="-7486.98" rx="173.53" ry="41.3"/>
<text text-anchor="middle" x="1470" y="-7499.58" font-family="Times,serif" font-size="14.00">调用 GitLab API 获取项目详情 (GetProject)</text>
<text text-anchor="middle" x="1470" y="-7482.78" font-family="Times,serif" font-size="14.00">- 输入: MR项目ID (mr.ProjectID)</text>
<text text-anchor="middle" x="1470" y="-7465.98" font-family="Times,serif" font-size="14.00">- 输出: 项目对象 (gitProject), 错误 (err)</text>
</g>
<!-- extract_mr_info&#45;&gt;fetch_project_details -->
<g id="edge5" class="edge">
<title>extract_mr_info-&gt;fetch_project_details</title>
<path fill="none" stroke="black" d="M1470,-7672.05C1470,-7631.02 1470,-7578.53 1470,-7540.1"/>
<polygon fill="black" stroke="black" points="1473.5,-7540.17 1470,-7530.17 1466.5,-7540.17 1473.5,-7540.17"/>
</g>
<!-- check_project_error -->
<g id="node7" class="node">
<title>check_project_error</title>
<ellipse fill="none" stroke="black" cx="1470" cy="-7283.69" rx="102.24" ry="18"/>
<text text-anchor="middle" x="1470" y="-7279.49" font-family="Times,serif" font-size="14.00">检查获取项目详情是否出错?</text>
</g>
<!-- fetch_project_details&#45;&gt;check_project_error -->
<g id="edge6" class="edge">
<title>fetch_project_details-&gt;check_project_error</title>
<path fill="none" stroke="black" d="M1470,-7445.25C1470,-7405.99 1470,-7347.62 1470,-7313.13"/>
<polygon fill="black" stroke="black" points="1473.5,-7313.51 1470,-7303.51 1466.5,-7313.51 1473.5,-7313.51"/>
</g>
<!-- log_project_error -->
<g id="node8" class="node">
<title>log_project_error</title>
<ellipse fill="none" stroke="black" cx="1339" cy="-7068.51" rx="61.74" ry="18"/>
<text text-anchor="middle" x="1339" y="-7064.31" font-family="Times,serif" font-size="14.00">记录错误并退出</text>
</g>
<!-- check_project_error&#45;&gt;log_project_error -->
<g id="edge7" class="edge">
<title>check_project_error-&gt;log_project_error</title>
<path fill="none" stroke="black" d="M1459.5,-7265.6C1436.75,-7228.57 1382.73,-7140.67 1355.42,-7096.23"/>
<polygon fill="black" stroke="black" points="1358.56,-7094.65 1350.34,-7087.97 1352.59,-7098.32 1358.56,-7094.65"/>
</g>
<!-- initialize_vars -->
<g id="node9" class="node">
<title>initialize_vars</title>
<ellipse fill="none" stroke="black" cx="1601" cy="-7068.51" rx="128.16" ry="53.18"/>
<text text-anchor="middle" x="1601" y="-7089.51" font-family="Times,serif" font-size="14.00">初始化变量:</text>
<text text-anchor="middle" x="1601" y="-7072.71" font-family="Times,serif" font-size="14.00">- 分页参数 (page, perPage)</text>
<text text-anchor="middle" x="1601" y="-7055.91" font-family="Times,serif" font-size="14.00">- 合并差异列表 (mergeDiffs)</text>
<text text-anchor="middle" x="1601" y="-7039.11" font-family="Times,serif" font-size="14.00">- 类型信息 (info for types.Info)</text>
</g>
<!-- check_project_error&#45;&gt;initialize_vars -->
<g id="edge8" class="edge">
<title>check_project_error-&gt;initialize_vars</title>
<path fill="none" stroke="black" d="M1480.5,-7265.6C1498.35,-7236.55 1535.45,-7176.18 1563.8,-7130.05"/>
<polygon fill="black" stroke="black" points="1566.66,-7132.08 1568.91,-7121.73 1560.69,-7128.42 1566.66,-7132.08"/>
</g>
<!-- loop_mr_diffs -->
<g id="node10" class="node">
<title>loop_mr_diffs</title>
<ellipse fill="none" stroke="black" cx="1601" cy="-6853.33" rx="79.71" ry="18"/>
<text text-anchor="middle" x="1601" y="-6849.13" font-family="Times,serif" font-size="14.00">循环获取 MR Diffs</text>
</g>
<!-- initialize_vars&#45;&gt;loop_mr_diffs -->
<g id="edge9" class="edge">
<title>initialize_vars-&gt;loop_mr_diffs</title>
<path fill="none" stroke="black" d="M1601,-7015.09C1601,-6973.37 1601,-6916.56 1601,-6882.85"/>
<polygon fill="black" stroke="black" points="1604.5,-6883.05 1601,-6873.05 1597.5,-6883.05 1604.5,-6883.05"/>
</g>
<!-- fetch_mr_diffs -->
<g id="node11" class="node">
<title>fetch_mr_diffs</title>
<ellipse fill="none" stroke="black" cx="1432" cy="-6650.04" rx="230.18" ry="41.3"/>
<text text-anchor="middle" x="1432" y="-6662.64" font-family="Times,serif" font-size="14.00">调用 GitLab API 获取 MR 差异 (ListMergeRequestDiffs)</text>
<text text-anchor="middle" x="1432" y="-6645.84" font-family="Times,serif" font-size="14.00">- 输入: projectID, mrIID, 分页选项</text>
<text text-anchor="middle" x="1432" y="-6629.04" font-family="Times,serif" font-size="14.00">- 输出: 差异列表 (mrDiffs), 响应 (response), 错误 (err)</text>
</g>
<!-- loop_mr_diffs&#45;&gt;fetch_mr_diffs -->
<g id="edge10" class="edge">
<title>loop_mr_diffs-&gt;fetch_mr_diffs</title>
<path fill="none" stroke="black" d="M1586.59,-6835.17C1561.72,-6805.55 1509.88,-6743.81 1473.1,-6699.99"/>
<polygon fill="black" stroke="black" points="1475.81,-6697.78 1466.7,-6692.37 1470.45,-6702.28 1475.81,-6697.78"/>
</g>
<!-- check_diff_error -->
<g id="node12" class="node">
<title>check_diff_error</title>
<ellipse fill="none" stroke="black" cx="1378" cy="-6446.74" rx="97.94" ry="18"/>
<text text-anchor="middle" x="1378" y="-6442.54" font-family="Times,serif" font-size="14.00">检查获取 Diffs 是否出错?</text>
</g>
<!-- fetch_mr_diffs&#45;&gt;check_diff_error -->
<g id="edge11" class="edge">
<title>fetch_mr_diffs-&gt;check_diff_error</title>
<path fill="none" stroke="black" d="M1421.07,-6608.3C1410.5,-6568.88 1394.74,-6510.16 1385.51,-6475.73"/>
<polygon fill="black" stroke="black" points="1389.01,-6475.27 1383.04,-6466.51 1382.25,-6477.08 1389.01,-6475.27"/>
</g>
<!-- log_diff_error -->
<g id="node13" class="node">
<title>log_diff_error</title>
<ellipse fill="none" stroke="black" cx="1378" cy="-6266.74" rx="61.74" ry="18"/>
<text text-anchor="middle" x="1378" y="-6262.54" font-family="Times,serif" font-size="14.00">记录错误并退出</text>
</g>
<!-- check_diff_error&#45;&gt;log_diff_error -->
<g id="edge12" class="edge">
<title>check_diff_error-&gt;log_diff_error</title>
<path fill="none" stroke="black" d="M1378,-6428.35C1378,-6397.65 1378,-6333.55 1378,-6296.29"/>
<polygon fill="black" stroke="black" points="1381.5,-6296.72 1378,-6286.72 1374.5,-6296.72 1381.5,-6296.72"/>
</g>
<!-- process_diffs_loop -->
<g id="node14" class="node">
<title>process_diffs_loop</title>
<ellipse fill="none" stroke="black" cx="1157" cy="-6266.74" rx="73" ry="18"/>
<text text-anchor="middle" x="1157" y="-6262.54" font-family="Times,serif" font-size="14.00">循环处理每个 Diff</text>
</g>
<!-- check_diff_error&#45;&gt;process_diffs_loop -->
<g id="edge13" class="edge">
<title>check_diff_error-&gt;process_diffs_loop</title>
<path fill="none" stroke="black" d="M1357.21,-6429C1317.7,-6397.18 1231.69,-6327.9 1186.34,-6291.38"/>
<polygon fill="black" stroke="black" points="1188.74,-6288.81 1178.75,-6285.26 1184.35,-6294.26 1188.74,-6288.81"/>
</g>
<!-- filter_go_files -->
<g id="node15" class="node">
<title>filter_go_files</title>
<ellipse fill="none" stroke="black" cx="1048" cy="-6086.74" rx="219.45" ry="18"/>
<text text-anchor="middle" x="1048" y="-6082.54" font-family="Times,serif" font-size="14.00">过滤非 Go 源文件或特定 Go 文件 (如 .pb.go, wire_gen.go)</text>
</g>
<!-- process_diffs_loop&#45;&gt;filter_go_files -->
<g id="edge14" class="edge">
<title>process_diffs_loop-&gt;filter_go_files</title>
<path fill="none" stroke="black" d="M1146.55,-6248.68C1127.55,-6217.66 1087.24,-6151.82 1064.5,-6114.68"/>
<polygon fill="black" stroke="black" points="1067.58,-6113.01 1059.37,-6106.31 1061.61,-6116.67 1067.58,-6113.01"/>
</g>
<!-- parse_hunks -->
<g id="node16" class="node">
<title>parse_hunks</title>
<ellipse fill="none" stroke="black" cx="1048" cy="-5883.45" rx="177.66" ry="41.3"/>
<text text-anchor="middle" x="1048" y="-5896.05" font-family="Times,serif" font-size="14.00">解析 Diff 中的 Hunks (sg_diff.ParseHunks)</text>
<text text-anchor="middle" x="1048" y="-5879.25" font-family="Times,serif" font-size="14.00">- 输入: diff.Diff</text>
<text text-anchor="middle" x="1048" y="-5862.45" font-family="Times,serif" font-size="14.00">- 输出: Hunks 列表 (diffs), 错误 (err)</text>
</g>
<!-- filter_go_files&#45;&gt;parse_hunks -->
<g id="edge15" class="edge">
<title>filter_go_files-&gt;parse_hunks</title>
<path fill="none" stroke="black" d="M1048,-6068.58C1048,-6039.61 1048,-5979.88 1048,-5936.28"/>
<polygon fill="black" stroke="black" points="1051.5,-5936.53 1048,-5926.53 1044.5,-5936.53 1051.5,-5936.53"/>
</g>
<!-- check_pagination -->
<g id="node17" class="node">
<title>check_pagination</title>
<ellipse fill="none" stroke="black" cx="915" cy="-5093.56" rx="80.51" ry="18"/>
<text text-anchor="middle" x="915" y="-5089.36" font-family="Times,serif" font-size="14.00">是否已获取所有页面?</text>
</g>
<!-- filter_go_files&#45;&gt;check_pagination -->
<g id="edge16" class="edge">
<title>filter_go_files-&gt;check_pagination</title>
<path fill="none" stroke="black" d="M991.08,-6068.99C861.57,-6027 556,-5904.26 556,-5681.15 556,-5681.15 556,-5681.15 556,-5499.15 556,-5386.13 558.41,-5343.82 629,-5255.56 688.55,-5181.11 792.43,-5135.2 857.26,-5112.33"/>
<polygon fill="black" stroke="black" points="858.36,-5115.66 866.67,-5109.09 856.07,-5109.04 858.36,-5115.66"/>
</g>
<!-- check_hunk_error -->
<g id="node18" class="node">
<title>check_hunk_error</title>
<ellipse fill="none" stroke="black" cx="1048" cy="-5680.15" rx="103.31" ry="18"/>
<text text-anchor="middle" x="1048" y="-5675.95" font-family="Times,serif" font-size="14.00">检查解析 Hunks 是否出错?</text>
</g>
<!-- parse_hunks&#45;&gt;check_hunk_error -->
<g id="edge17" class="edge">
<title>parse_hunks-&gt;check_hunk_error</title>
<path fill="none" stroke="black" d="M1048,-5841.71C1048,-5802.46 1048,-5744.08 1048,-5709.59"/>
<polygon fill="black" stroke="black" points="1051.5,-5709.97 1048,-5699.97 1044.5,-5709.97 1051.5,-5709.97"/>
</g>
<!-- increment_page -->
<g id="node24" class="node">
<title>increment_page</title>
<ellipse fill="none" stroke="black" cx="1469" cy="-4913.56" rx="77.98" ry="18"/>
<text text-anchor="middle" x="1469" y="-4909.36" font-family="Times,serif" font-size="14.00">页码递增 (page++)</text>
</g>
<!-- check_pagination&#45;&gt;increment_page -->
<g id="edge25" class="edge">
<title>check_pagination-&gt;increment_page</title>
<path fill="none" stroke="black" d="M959.4,-5078.3C1059.81,-5046.03 1304.13,-4967.53 1413.95,-4932.25"/>
<polygon fill="black" stroke="black" points="1414.88,-4935.62 1423.33,-4929.23 1412.74,-4928.96 1414.88,-4935.62"/>
</g>
<!-- exit_diff_loop -->
<g id="node25" class="node">
<title>exit_diff_loop</title>
<ellipse fill="none" stroke="black" cx="915" cy="-4913.56" rx="75.41" ry="18"/>
<text text-anchor="middle" x="915" y="-4909.36" font-family="Times,serif" font-size="14.00">退出 Diff 获取循环</text>
</g>
<!-- check_pagination&#45;&gt;exit_diff_loop -->
<g id="edge26" class="edge">
<title>check_pagination-&gt;exit_diff_loop</title>
<path fill="none" stroke="black" d="M915,-5075.17C915,-5044.46 915,-4980.37 915,-4943.1"/>
<polygon fill="black" stroke="black" points="918.5,-4943.54 915,-4933.54 911.5,-4943.54 918.5,-4943.54"/>
</g>
<!-- log_hunk_error -->
<g id="node19" class="node">
<title>log_hunk_error</title>
<ellipse fill="none" stroke="black" cx="1034" cy="-5500.15" rx="89.89" ry="18"/>
<text text-anchor="middle" x="1034" y="-5495.95" font-family="Times,serif" font-size="14.00">记录错误并退出 (Fatal)</text>
</g>
<!-- check_hunk_error&#45;&gt;log_hunk_error -->
<g id="edge18" class="edge">
<title>check_hunk_error-&gt;log_hunk_error</title>
<path fill="none" stroke="black" d="M1046.63,-5661.76C1044.22,-5631.05 1039.18,-5566.96 1036.24,-5529.7"/>
<polygon fill="black" stroke="black" points="1039.77,-5529.82 1035.49,-5520.12 1032.79,-5530.37 1039.77,-5529.82"/>
</g>
<!-- process_hunks_loop -->
<g id="node20" class="node">
<title>process_hunks_loop</title>
<ellipse fill="none" stroke="black" cx="1274" cy="-5500.15" rx="78.37" ry="18"/>
<text text-anchor="middle" x="1274" y="-5495.95" font-family="Times,serif" font-size="14.00">循环处理每个 Hunk</text>
</g>
<!-- check_hunk_error&#45;&gt;process_hunks_loop -->
<g id="edge19" class="edge">
<title>check_hunk_error-&gt;process_hunks_loop</title>
<path fill="none" stroke="black" d="M1069.67,-5662.08C1110.19,-5630.17 1197.49,-5561.41 1243.77,-5524.96"/>
<polygon fill="black" stroke="black" points="1245.85,-5527.78 1251.54,-5518.84 1241.52,-5522.28 1245.85,-5527.78"/>
</g>
<!-- create_diff_chunk -->
<g id="node21" class="node">
<title>create_diff_chunk</title>
<ellipse fill="none" stroke="black" cx="1536" cy="-5296.86" rx="153.19" ry="41.3"/>
<text text-anchor="middle" x="1536" y="-5309.46" font-family="Times,serif" font-size="14.00">创建 DiffChunk 对象 (vo.DiffChunk)</text>
<text text-anchor="middle" x="1536" y="-5292.66" font-family="Times,serif" font-size="14.00">- 包含 Hunk</text>
<text text-anchor="middle" x="1536" y="-5275.86" font-family="Times,serif" font-size="14.00">- 计算变更详情 (dc.Detail())</text>
</g>
<!-- process_hunks_loop&#45;&gt;create_diff_chunk -->
<g id="edge20" class="edge">
<title>process_hunks_loop-&gt;create_diff_chunk</title>
<path fill="none" stroke="black" d="M1295.44,-5482.68C1334.71,-5452.51 1419.43,-5387.42 1477.09,-5343.12"/>
<polygon fill="black" stroke="black" points="1479.1,-5345.98 1484.9,-5337.12 1474.84,-5340.43 1479.1,-5345.98"/>
</g>
<!-- append_merge_diff -->
<g id="node22" class="node">
<title>append_merge_diff</title>
<ellipse fill="none" stroke="black" cx="915" cy="-5296.86" rx="249.74" ry="18"/>
<text text-anchor="middle" x="915" y="-5292.66" font-family="Times,serif" font-size="14.00">将处理后的 MergeDiff (包含 DiffChunks) 添加到 mergeDiffs 列表</text>
</g>
<!-- process_hunks_loop&#45;&gt;append_merge_diff -->
<g id="edge21" class="edge">
<title>process_hunks_loop-&gt;append_merge_diff</title>
<path fill="none" stroke="black" d="M1245.23,-5483.02C1181.66,-5447.38 1029.1,-5361.84 955.75,-5320.7"/>
<polygon fill="black" stroke="black" points="957.69,-5317.78 947.26,-5315.94 954.27,-5323.89 957.69,-5317.78"/>
</g>
<!-- append_diff_chunk -->
<g id="node23" class="node">
<title>append_diff_chunk</title>
<ellipse fill="none" stroke="black" cx="1405" cy="-5093.56" rx="101.16" ry="18"/>
<text text-anchor="middle" x="1405" y="-5089.36" font-family="Times,serif" font-size="14.00">将 DiffChunk 添加到列表</text>
</g>
<!-- create_diff_chunk&#45;&gt;append_diff_chunk -->
<g id="edge22" class="edge">
<title>create_diff_chunk-&gt;append_diff_chunk</title>
<path fill="none" stroke="black" d="M1510.12,-5256.09C1484.13,-5216.15 1444.8,-5155.72 1422.35,-5121.21"/>
<polygon fill="black" stroke="black" points="1425.43,-5119.54 1417.04,-5113.07 1419.56,-5123.36 1425.43,-5119.54"/>
</g>
<!-- append_merge_diff&#45;&gt;check_pagination -->
<g id="edge24" class="edge">
<title>append_merge_diff-&gt;check_pagination</title>
<path fill="none" stroke="black" d="M915,-5278.69C915,-5244.17 915,-5165.98 915,-5123.45"/>
<polygon fill="black" stroke="black" points="918.5,-5123.48 915,-5113.48 911.5,-5123.48 918.5,-5123.48"/>
</g>
<!-- append_diff_chunk&#45;&gt;process_hunks_loop -->
<g id="edge23" class="edge">
<title>append_diff_chunk-&gt;process_hunks_loop</title>
<path fill="none" stroke="black" d="M1398.42,-5111.85C1387.33,-5141.21 1364.4,-5202.8 1347,-5255.56 1321.49,-5332.92 1294.83,-5425.31 1281.93,-5470.87"/>
<polygon fill="black" stroke="black" points="1278.57,-5469.89 1279.22,-5480.47 1285.31,-5471.79 1278.57,-5469.89"/>
</g>
<!-- increment_page&#45;&gt;loop_mr_diffs -->
<g id="edge27" class="edge">
<title>increment_page-&gt;loop_mr_diffs</title>
<path fill="none" stroke="black" d="M1499.36,-4930.57C1580.81,-4975.94 1798,-5114 1798,-5295.86 1798,-6447.74 1798,-6447.74 1798,-6447.74 1798,-6605.28 1675.77,-6765.67 1623.87,-6826.67"/>
<polygon fill="black" stroke="black" points="1621.33,-6824.26 1617.45,-6834.12 1626.63,-6828.83 1621.33,-6824.26"/>
</g>
<!-- initialize_fn_lists -->
<g id="node26" class="node">
<title>initialize_fn_lists</title>
<ellipse fill="none" stroke="black" cx="915" cy="-4710.27" rx="80.88" ry="41.3"/>
<text text-anchor="middle" x="915" y="-4722.87" font-family="Times,serif" font-size="14.00">初始化函数信息列表:</text>
<text text-anchor="middle" x="915" y="-4706.07" font-family="Times,serif" font-size="14.00">- allNewFnList</text>
<text text-anchor="middle" x="915" y="-4689.27" font-family="Times,serif" font-size="14.00">- allOldFnList</text>
</g>
<!-- exit_diff_loop&#45;&gt;initialize_fn_lists -->
<g id="edge28" class="edge">
<title>exit_diff_loop-&gt;initialize_fn_lists</title>
<path fill="none" stroke="black" d="M915,-4895.4C915,-4866.42 915,-4806.7 915,-4763.1"/>
<polygon fill="black" stroke="black" points="918.5,-4763.35 915,-4753.35 911.5,-4763.35 918.5,-4763.35"/>
</g>
<!-- process_merge_diffs_loop -->
<g id="node27" class="node">
<title>process_merge_diffs_loop</title>
<ellipse fill="none" stroke="black" cx="915" cy="-4506.97" rx="86.4" ry="18"/>
<text text-anchor="middle" x="915" y="-4502.77" font-family="Times,serif" font-size="14.00">循环处理 mergeDiffs</text>
</g>
<!-- initialize_fn_lists&#45;&gt;process_merge_diffs_loop -->
<g id="edge29" class="edge">
<title>initialize_fn_lists-&gt;process_merge_diffs_loop</title>
<path fill="none" stroke="black" d="M915,-4668.53C915,-4629.27 915,-4570.9 915,-4536.41"/>
<polygon fill="black" stroke="black" points="918.5,-4536.79 915,-4526.79 911.5,-4536.79 918.5,-4536.79"/>
</g>
<!-- filter_go_files_again -->
<g id="node28" class="node">
<title>filter_go_files_again</title>
<ellipse fill="none" stroke="black" cx="385" cy="-4326.97" rx="138.45" ry="18"/>
<text text-anchor="middle" x="385" y="-4322.77" font-family="Times,serif" font-size="14.00">再次过滤非 Go 源文件或特定 Go 文件</text>
</g>
<!-- process_merge_diffs_loop&#45;&gt;filter_go_files_again -->
<g id="edge30" class="edge">
<title>process_merge_diffs_loop-&gt;filter_go_files_again</title>
<path fill="none" stroke="black" d="M870.72,-4491.1C775.47,-4459.11 551.08,-4383.75 443.94,-4347.77"/>
<polygon fill="black" stroke="black" points="445.36,-4344.55 434.76,-4344.68 443.13,-4351.18 445.36,-4344.55"/>
</g>
<!-- fetch_new_file -->
<g id="node29" class="node">
<title>fetch_new_file</title>
<ellipse fill="none" stroke="black" cx="385" cy="-4123.67" rx="188.65" ry="41.3"/>
<text text-anchor="middle" x="385" y="-4136.27" font-family="Times,serif" font-size="14.00">获取新版本文件内容 (file_api.FetchFileContent)</text>
<text text-anchor="middle" x="385" y="-4119.47" font-family="Times,serif" font-size="14.00">- 输入: projectID, headSHA, diff.NewPath</text>
<text text-anchor="middle" x="385" y="-4102.67" font-family="Times,serif" font-size="14.00">- 输出: 文件内容 (file), 错误 (err)</text>
</g>
<!-- filter_go_files_again&#45;&gt;fetch_new_file -->
<g id="edge31" class="edge">
<title>filter_go_files_again-&gt;fetch_new_file</title>
<path fill="none" stroke="black" d="M385,-4308.81C385,-4279.83 385,-4220.11 385,-4176.51"/>
<polygon fill="black" stroke="black" points="388.5,-4176.76 385,-4166.76 381.5,-4176.76 388.5,-4176.76"/>
</g>
<!-- end_process_merge_diffs_loop -->
<g id="node30" class="node">
<title>end_process_merge_diffs_loop</title>
<ellipse fill="none" stroke="black" cx="292" cy="-198" rx="88.81" ry="18"/>
<text text-anchor="middle" x="292" y="-193.8" font-family="Times,serif" font-size="14.00">结束 mergeDiffs 循环</text>
</g>
<!-- filter_go_files_again&#45;&gt;end_process_merge_diffs_loop -->
<g id="edge32" class="edge">
<title>filter_go_files_again-&gt;end_process_merge_diffs_loop</title>
<path fill="none" stroke="black" d="M348.08,-4309.24C251.72,-4262.74 0,-4122.09 0,-3921.38 0,-3921.38 0,-3921.38 0,-568.42 0,-405.74 176.43,-272.76 255.01,-221.6"/>
<polygon fill="black" stroke="black" points="256.73,-224.65 263.26,-216.31 252.95,-218.76 256.73,-224.65"/>
</g>
<!-- check_fetch_new_error -->
<g id="node31" class="node">
<title>check_fetch_new_error</title>
<ellipse fill="none" stroke="black" cx="385" cy="-3920.38" rx="94.99" ry="18"/>
<text text-anchor="middle" x="385" y="-3916.18" font-family="Times,serif" font-size="14.00">检查获取新文件是否出错?</text>
</g>
<!-- fetch_new_file&#45;&gt;check_fetch_new_error -->
<g id="edge33" class="edge">
<title>fetch_new_file-&gt;check_fetch_new_error</title>
<path fill="none" stroke="black" d="M385,-4081.94C385,-4042.68 385,-3984.31 385,-3949.82"/>
<polygon fill="black" stroke="black" points="388.5,-3950.2 385,-3940.2 381.5,-3950.2 388.5,-3950.2"/>
</g>
<!-- end_process_merge_diffs_loop&#45;&gt;process_merge_diffs_loop -->
<g id="edge64" class="edge">
<title>end_process_merge_diffs_loop-&gt;process_merge_diffs_loop</title>
<path fill="none" stroke="black" d="M373.36,-205.47C649.23,-229.61 1532,-325.82 1532,-568.42 1532,-4124.67 1532,-4124.67 1532,-4124.67 1532,-4373.48 1164.45,-4465.36 996.72,-4494.33"/>
<polygon fill="black" stroke="black" points="996.25,-4490.86 986.97,-4495.98 997.41,-4497.77 996.25,-4490.86"/>
</g>
<!-- end -->
<g id="node55" class="node">
<title>end</title>
<ellipse fill="none" stroke="black" cx="292" cy="-18" rx="69.24" ry="18"/>
<text text-anchor="middle" x="292" y="-13.8" font-family="Times,serif" font-size="14.00">结束 MrProcess</text>
</g>
<!-- end_process_merge_diffs_loop&#45;&gt;end -->
<g id="edge65" class="edge">
<title>end_process_merge_diffs_loop-&gt;end</title>
<path fill="none" stroke="black" d="M292,-179.61C292,-148.9 292,-84.81 292,-47.54"/>
<polygon fill="black" stroke="black" points="295.5,-47.98 292,-37.98 288.5,-47.98 295.5,-47.98"/>
</g>
<!-- log_fetch_new_error -->
<g id="node32" class="node">
<title>log_fetch_new_error</title>
<ellipse fill="none" stroke="black" cx="171" cy="-3717.08" rx="61.74" ry="18"/>
<text text-anchor="middle" x="171" y="-3712.88" font-family="Times,serif" font-size="14.00">记录错误并退出</text>
</g>
<!-- check_fetch_new_error&#45;&gt;log_fetch_new_error -->
<g id="edge34" class="edge">
<title>check_fetch_new_error-&gt;log_fetch_new_error</title>
<path fill="none" stroke="black" d="M367.12,-3902.56C329.26,-3866.95 240.88,-3783.81 196.86,-3742.41"/>
<polygon fill="black" stroke="black" points="199.46,-3740.05 189.78,-3735.75 194.66,-3745.15 199.46,-3740.05"/>
</g>
<!-- parse_new_file -->
<g id="node33" class="node">
<title>parse_new_file</title>
<ellipse fill="none" stroke="black" cx="472" cy="-3717.08" rx="167.21" ry="41.3"/>
<text text-anchor="middle" x="472" y="-3729.68" font-family="Times,serif" font-size="14.00">解析新版本文件 (file_api.ParseSingleFile)</text>
<text text-anchor="middle" x="472" y="-3712.88" font-family="Times,serif" font-size="14.00">- 输入: diff.NewPath, file 内容, info</text>
<text text-anchor="middle" x="472" y="-3696.08" font-family="Times,serif" font-size="14.00">- 输出: 函数信息列表 (fnList)</text>
</g>
<!-- check_fetch_new_error&#45;&gt;parse_new_file -->
<g id="edge35" class="edge">
<title>check_fetch_new_error-&gt;parse_new_file</title>
<path fill="none" stroke="black" d="M392.42,-3902.22C405.06,-3872.97 431.24,-3812.39 450.13,-3768.69"/>
<polygon fill="black" stroke="black" points="453.28,-3770.21 454.04,-3759.64 446.86,-3767.44 453.28,-3770.21"/>
</g>
<!-- append_new_fn_list -->
<g id="node34" class="node">
<title>append_new_fn_list</title>
<ellipse fill="none" stroke="black" cx="472" cy="-3513.79" rx="123.43" ry="18"/>
<text text-anchor="middle" x="472" y="-3509.59" font-family="Times,serif" font-size="14.00">将 fnList 添加到 allNewFnList</text>
</g>
<!-- parse_new_file&#45;&gt;append_new_fn_list -->
<g id="edge36" class="edge">
<title>parse_new_file-&gt;append_new_fn_list</title>
<path fill="none" stroke="black" d="M472,-3675.35C472,-3636.09 472,-3577.72 472,-3543.23"/>
<polygon fill="black" stroke="black" points="475.5,-3543.61 472,-3533.61 468.5,-3543.61 475.5,-3543.61"/>
</g>
<!-- fetch_old_file -->
<g id="node35" class="node">
<title>fetch_old_file</title>
<ellipse fill="none" stroke="black" cx="472" cy="-3310.49" rx="188.65" ry="41.3"/>
<text text-anchor="middle" x="472" y="-3323.09" font-family="Times,serif" font-size="14.00">获取旧版本文件内容 (file_api.FetchFileContent)</text>
<text text-anchor="middle" x="472" y="-3306.29" font-family="Times,serif" font-size="14.00">- 输入: projectID, startSHA, diff.NewPath</text>
<text text-anchor="middle" x="472" y="-3289.49" font-family="Times,serif" font-size="14.00">- 输出: 文件内容 (file), 错误 (err)</text>
</g>
<!-- append_new_fn_list&#45;&gt;fetch_old_file -->
<g id="edge37" class="edge">
<title>append_new_fn_list-&gt;fetch_old_file</title>
<path fill="none" stroke="black" d="M472,-3495.62C472,-3466.65 472,-3406.93 472,-3363.33"/>
<polygon fill="black" stroke="black" points="475.5,-3363.57 472,-3353.57 468.5,-3363.57 475.5,-3363.57"/>
</g>
<!-- check_fetch_old_error -->
<g id="node36" class="node">
<title>check_fetch_old_error</title>
<ellipse fill="none" stroke="black" cx="472" cy="-3107.2" rx="94.99" ry="18"/>
<text text-anchor="middle" x="472" y="-3103" font-family="Times,serif" font-size="14.00">检查获取旧文件是否出错?</text>
</g>
<!-- fetch_old_file&#45;&gt;check_fetch_old_error -->
<g id="edge38" class="edge">
<title>fetch_old_file-&gt;check_fetch_old_error</title>
<path fill="none" stroke="black" d="M472,-3268.76C472,-3229.5 472,-3171.13 472,-3136.64"/>
<polygon fill="black" stroke="black" points="475.5,-3137.02 472,-3127.02 468.5,-3137.02 475.5,-3137.02"/>
</g>
<!-- log_fetch_old_error -->
<g id="node37" class="node">
<title>log_fetch_old_error</title>
<ellipse fill="none" stroke="black" cx="171" cy="-2903.9" rx="61.74" ry="18"/>
<text text-anchor="middle" x="171" y="-2899.7" font-family="Times,serif" font-size="14.00">记录错误并退出</text>
</g>
<!-- check_fetch_old_error&#45;&gt;log_fetch_old_error -->
<g id="edge39" class="edge">
<title>check_fetch_old_error-&gt;log_fetch_old_error</title>
<path fill="none" stroke="black" d="M446.85,-3089.38C392.65,-3053.13 264.8,-2967.63 204.1,-2927.04"/>
<polygon fill="black" stroke="black" points="206.13,-2924.18 195.87,-2921.53 202.24,-2930 206.13,-2924.18"/>
</g>
<!-- parse_old_file -->
<g id="node38" class="node">
<title>parse_old_file</title>
<ellipse fill="none" stroke="black" cx="472" cy="-2903.9" rx="167.21" ry="41.3"/>
<text text-anchor="middle" x="472" y="-2916.5" font-family="Times,serif" font-size="14.00">解析旧版本文件 (file_api.ParseSingleFile)</text>
<text text-anchor="middle" x="472" y="-2899.7" font-family="Times,serif" font-size="14.00">- 输入: diff.NewPath, file 内容, info</text>
<text text-anchor="middle" x="472" y="-2882.9" font-family="Times,serif" font-size="14.00">- 输出: 函数信息列表 (oldFnList)</text>
</g>
<!-- check_fetch_old_error&#45;&gt;parse_old_file -->
<g id="edge40" class="edge">
<title>check_fetch_old_error-&gt;parse_old_file</title>
<path fill="none" stroke="black" d="M472,-3089.03C472,-3060.06 472,-3000.34 472,-2956.74"/>
<polygon fill="black" stroke="black" points="475.5,-2956.98 472,-2946.98 468.5,-2956.98 475.5,-2956.98"/>
</g>
<!-- append_old_fn_list -->
<g id="node39" class="node">
<title>append_old_fn_list</title>
<ellipse fill="none" stroke="black" cx="443" cy="-2700.6" rx="134.17" ry="18"/>
<text text-anchor="middle" x="443" y="-2696.4" font-family="Times,serif" font-size="14.00">将 oldFnList 添加到 allOldFnList</text>
</g>
<!-- parse_old_file&#45;&gt;append_old_fn_list -->
<g id="edge41" class="edge">
<title>parse_old_file-&gt;append_old_fn_list</title>
<path fill="none" stroke="black" d="M466.13,-2862.17C460.48,-2822.91 452.07,-2764.53 447.1,-2730.05"/>
<polygon fill="black" stroke="black" points="450.6,-2729.81 445.71,-2720.41 443.67,-2730.81 450.6,-2729.81"/>
</g>
<!-- check_diff_chunks_exist -->
<g id="node40" class="node">
<title>check_diff_chunks_exist</title>
<ellipse fill="none" stroke="black" cx="415" cy="-2520.6" rx="125.57" ry="18"/>
<text text-anchor="middle" x="415" y="-2516.4" font-family="Times,serif" font-size="14.00">检查 diff.DiffChunks 是否存在?</text>
</g>
<!-- append_old_fn_list&#45;&gt;check_diff_chunks_exist -->
<g id="edge42" class="edge">
<title>append_old_fn_list-&gt;check_diff_chunks_exist</title>
<path fill="none" stroke="black" d="M440.26,-2682.21C435.43,-2651.51 425.35,-2587.42 419.49,-2550.15"/>
<polygon fill="black" stroke="black" points="422.99,-2549.9 417.98,-2540.56 416.08,-2550.98 422.99,-2549.9"/>
</g>
<!-- check_diff_chunks_exist&#45;&gt;end_process_merge_diffs_loop -->
<g id="edge44" class="edge">
<title>check_diff_chunks_exist-&gt;end_process_merge_diffs_loop</title>
<path fill="none" stroke="black" d="M388.72,-2502.63C321.03,-2456.63 146,-2322.14 146,-2161.6 146,-2161.6 146,-2161.6 146,-568.42 146,-431.19 234.21,-284.1 273.5,-225.39"/>
<polygon fill="black" stroke="black" points="276.23,-227.6 278.95,-217.36 270.44,-223.66 276.23,-227.6"/>
</g>
<!-- process_diff_chunks_loop -->
<g id="node41" class="node">
<title>process_diff_chunks_loop</title>
<ellipse fill="none" stroke="black" cx="415" cy="-2340.6" rx="104.38" ry="18"/>
<text text-anchor="middle" x="415" y="-2336.4" font-family="Times,serif" font-size="14.00">循环处理 diff.DiffChunks</text>
</g>
<!-- check_diff_chunks_exist&#45;&gt;process_diff_chunks_loop -->
<g id="edge43" class="edge">
<title>check_diff_chunks_exist-&gt;process_diff_chunks_loop</title>
<path fill="none" stroke="black" d="M415,-2502.21C415,-2471.51 415,-2407.42 415,-2370.15"/>
<polygon fill="black" stroke="black" points="418.5,-2370.58 415,-2360.58 411.5,-2370.58 418.5,-2370.58"/>
</g>
<!-- get_hunk_section -->
<g id="node42" class="node">
<title>get_hunk_section</title>
<ellipse fill="none" stroke="black" cx="601" cy="-2160.6" rx="153.99" ry="18"/>
<text text-anchor="middle" x="601" y="-2156.4" font-family="Times,serif" font-size="14.00">获取 Hunk Section (dc.Hunk.Section)</text>
</g>
<!-- process_diff_chunks_loop&#45;&gt;get_hunk_section -->
<g id="edge45" class="edge">
<title>process_diff_chunks_loop-&gt;get_hunk_section</title>
<path fill="none" stroke="black" d="M432.83,-2322.54C465.8,-2290.99 536.41,-2223.42 574.82,-2186.66"/>
<polygon fill="black" stroke="black" points="577.05,-2189.37 581.85,-2179.93 572.21,-2184.31 577.05,-2189.37"/>
</g>
<!-- check_section_exists -->
<g id="node43" class="node">
<title>check_section_exists</title>
<ellipse fill="none" stroke="black" cx="646" cy="-1980.6" rx="99.55" ry="18"/>
<text text-anchor="middle" x="646" y="-1976.4" font-family="Times,serif" font-size="14.00">Hunk Section 是否存在?</text>
</g>
<!-- get_hunk_section&#45;&gt;check_section_exists -->
<g id="edge46" class="edge">
<title>get_hunk_section-&gt;check_section_exists</title>
<path fill="none" stroke="black" d="M605.4,-2142.21C613.16,-2111.51 629.36,-2047.42 638.78,-2010.15"/>
<polygon fill="black" stroke="black" points="642.16,-2011.09 641.21,-2000.53 635.37,-2009.37 642.16,-2011.09"/>
</g>
<!-- get_func_signature -->
<g id="node44" class="node">
<title>get_func_signature</title>
<ellipse fill="none" stroke="black" cx="646" cy="-1777.31" rx="170.22" ry="41.3"/>
<text text-anchor="middle" x="646" y="-1789.91" font-family="Times,serif" font-size="14.00">获取函数签名 (file_api.GetCompelteFunc)</text>
<text text-anchor="middle" x="646" y="-1773.11" font-family="Times,serif" font-size="14.00">- 输入: dc.Hunk.Section</text>
<text text-anchor="middle" x="646" y="-1756.31" font-family="Times,serif" font-size="14.00">- 输出: 函数签名 (fnSignature), 错误 (err)</text>
</g>
<!-- check_section_exists&#45;&gt;get_func_signature -->
<g id="edge47" class="edge">
<title>check_section_exists-&gt;get_func_signature</title>
<path fill="none" stroke="black" d="M646,-1962.44C646,-1933.47 646,-1873.74 646,-1830.15"/>
<polygon fill="black" stroke="black" points="649.5,-1830.39 646,-1820.39 642.5,-1830.39 649.5,-1830.39"/>
</g>
<!-- check_fn_body_and_added -->
<g id="node45" class="node">
<title>check_fn_body_and_added</title>
<ellipse fill="none" stroke="black" cx="994" cy="-987.42" rx="227.12" ry="18"/>
<text text-anchor="middle" x="994" y="-983.22" font-family="Times,serif" font-size="14.00">检查 fnBody 是否为空 或 Hunk 中没有新增行 (Added &lt;= 0)?</text>
</g>
<!-- check_section_exists&#45;&gt;check_fn_body_and_added -->
<g id="edge48" class="edge">
<title>check_section_exists-&gt;check_fn_body_and_added</title>
<path fill="none" stroke="black" d="M678.04,-1963.09C765.18,-1915.61 1000,-1769.61 1000,-1575.01 1000,-1575.01 1000,-1575.01 1000,-1369.72 1000,-1238.13 996.38,-1081.04 994.77,-1017.36"/>
<polygon fill="black" stroke="black" points="998.27,-1017.32 994.51,-1007.42 991.27,-1017.5 998.27,-1017.32"/>
</g>
<!-- check_get_sig_error -->
<g id="node46" class="node">
<title>check_get_sig_error</title>
<ellipse fill="none" stroke="black" cx="646" cy="-1574.01" rx="109.48" ry="18"/>
<text text-anchor="middle" x="646" y="-1569.81" font-family="Times,serif" font-size="14.00">获取签名是否出错或签名为空?</text>
</g>
<!-- get_func_signature&#45;&gt;check_get_sig_error -->
<g id="edge49" class="edge">
<title>get_func_signature-&gt;check_get_sig_error</title>
<path fill="none" stroke="black" d="M646,-1735.57C646,-1696.32 646,-1637.94 646,-1603.46"/>
<polygon fill="black" stroke="black" points="649.5,-1603.84 646,-1593.84 642.5,-1603.84 649.5,-1603.84"/>
</g>
<!-- skip_to_next_chunk -->
<g id="node52" class="node">
<title>skip_to_next_chunk</title>
<ellipse fill="none" stroke="black" cx="862" cy="-784.13" rx="92.32" ry="18"/>
<text text-anchor="middle" x="862" y="-779.93" font-family="Times,serif" font-size="14.00">继续下一次 Chunk 循环</text>
</g>
<!-- check_fn_body_and_added&#45;&gt;skip_to_next_chunk -->
<g id="edge57" class="edge">
<title>check_fn_body_and_added-&gt;skip_to_next_chunk</title>
<path fill="none" stroke="black" d="M982.75,-969.26C959.71,-934.13 907.03,-853.79 879.49,-811.8"/>
<polygon fill="black" stroke="black" points="882.47,-809.95 874.06,-803.51 876.61,-813.79 882.47,-809.95"/>
</g>
<!-- call_ai_cr -->
<g id="node53" class="node">
<title>call_ai_cr</title>
<ellipse fill="none" stroke="black" cx="1189" cy="-784.13" rx="156.77" ry="41.3"/>
<text text-anchor="middle" x="1189" y="-796.73" font-family="Times,serif" font-size="14.00">调用 AI 代码审查 (p.AiCr)</text>
<text text-anchor="middle" x="1189" y="-779.93" font-family="Times,serif" font-size="14.00">- 输入: fnBody, diff (ChunkToString)</text>
<text text-anchor="middle" x="1189" y="-763.13" font-family="Times,serif" font-size="14.00">- 输出: 评估结果 (evaluation)</text>
</g>
<!-- check_fn_body_and_added&#45;&gt;call_ai_cr -->
<g id="edge58" class="edge">
<title>check_fn_body_and_added-&gt;call_ai_cr</title>
<path fill="none" stroke="black" d="M1010.63,-969.26C1039.61,-939.34 1100.36,-876.63 1142.87,-832.75"/>
<polygon fill="black" stroke="black" points="1145.35,-835.22 1149.8,-825.6 1140.32,-830.34 1145.35,-835.22"/>
</g>
<!-- log_get_sig_error -->
<g id="node47" class="node">
<title>log_get_sig_error</title>
<ellipse fill="none" stroke="black" cx="499" cy="-1167.42" rx="97.95" ry="18"/>
<text text-anchor="middle" x="499" y="-1163.22" font-family="Times,serif" font-size="14.00">记录错误并继续下一次循环</text>
</g>
<!-- check_get_sig_error&#45;&gt;log_get_sig_error -->
<g id="edge50" class="edge">
<title>check_get_sig_error-&gt;log_get_sig_error</title>
<path fill="none" stroke="black" d="M632.82,-1555.71C611.84,-1527.28 570.96,-1468.25 549,-1412.01 519.95,-1337.63 506.78,-1243.57 501.7,-1197.09"/>
<polygon fill="black" stroke="black" points="505.2,-1196.91 500.69,-1187.33 498.24,-1197.64 505.2,-1196.91"/>
</g>
<!-- get_func_body -->
<g id="node48" class="node">
<title>get_func_body</title>
<ellipse fill="none" stroke="black" cx="738" cy="-1370.72" rx="153.18" ry="41.3"/>
<text text-anchor="middle" x="738" y="-1383.32" font-family="Times,serif" font-size="14.00">获取旧函数体 (file_api.GetFuncBody)</text>
<text text-anchor="middle" x="738" y="-1366.52" font-family="Times,serif" font-size="14.00">- 输入: oldFnList, fnSignature</text>
<text text-anchor="middle" x="738" y="-1349.72" font-family="Times,serif" font-size="14.00">- 输出: 函数体 (fnBody), 错误 (err)</text>
</g>
<!-- check_get_sig_error&#45;&gt;get_func_body -->
<g id="edge51" class="edge">
<title>check_get_sig_error-&gt;get_func_body</title>
<path fill="none" stroke="black" d="M653.84,-1555.85C667.21,-1526.6 694.9,-1466.03 714.87,-1422.33"/>
<polygon fill="black" stroke="black" points="718.04,-1423.82 719.01,-1413.27 711.67,-1420.91 718.04,-1423.82"/>
</g>
<!-- end_process_chunks_loop -->
<g id="node49" class="node">
<title>end_process_chunks_loop</title>
<ellipse fill="none" stroke="black" cx="541" cy="-378" rx="90.44" ry="18"/>
<text text-anchor="middle" x="541" y="-373.8" font-family="Times,serif" font-size="14.00">结束 DiffChunks 循环</text>
</g>
<!-- log_get_sig_error&#45;&gt;end_process_chunks_loop -->
<g id="edge52" class="edge">
<title>log_get_sig_error-&gt;end_process_chunks_loop</title>
<path fill="none" stroke="black" d="M498.3,-1149.42C495.51,-1076.22 486.43,-780.77 511,-540 515.79,-493.1 526.96,-439.41 534.24,-407.4"/>
<polygon fill="black" stroke="black" points="537.63,-408.26 536.47,-397.73 530.81,-406.68 537.63,-408.26"/>
</g>
<!-- check_get_body_error -->
<g id="node50" class="node">
<title>check_get_body_error</title>
<ellipse fill="none" stroke="black" cx="749" cy="-1167.42" rx="80.51" ry="18"/>
<text text-anchor="middle" x="749" y="-1163.22" font-family="Times,serif" font-size="14.00">获取函数体是否出错?</text>
</g>
<!-- get_func_body&#45;&gt;check_get_body_error -->
<g id="edge53" class="edge">
<title>get_func_body-&gt;check_get_body_error</title>
<path fill="none" stroke="black" d="M740.23,-1328.98C742.37,-1289.73 745.56,-1231.35 747.45,-1196.86"/>
<polygon fill="black" stroke="black" points="750.92,-1197.42 747.97,-1187.24 743.93,-1197.04 750.92,-1197.42"/>
</g>
<!-- end_process_chunks_loop&#45;&gt;end_process_merge_diffs_loop -->
<g id="edge63" class="edge">
<title>end_process_chunks_loop-&gt;end_process_merge_diffs_loop</title>
<path fill="none" stroke="black" d="M517.57,-360.25C472.99,-328.38 375.84,-258.94 324.81,-222.45"/>
<polygon fill="black" stroke="black" points="326.95,-219.68 316.78,-216.71 322.88,-225.37 326.95,-219.68"/>
</g>
<!-- end_process_chunks_loop&#45;&gt;process_diff_chunks_loop -->
<g id="edge62" class="edge">
<title>end_process_chunks_loop-&gt;process_diff_chunks_loop</title>
<path fill="none" stroke="black" d="M520.62,-395.95C461.15,-447.46 292,-609.24 292,-783.13 292,-1981.6 292,-1981.6 292,-1981.6 292,-2111.53 365.35,-2254.28 398.77,-2312.57"/>
<polygon fill="black" stroke="black" points="395.7,-2314.26 403.75,-2321.14 401.75,-2310.74 395.7,-2314.26"/>
</g>
<!-- check_get_body_error&#45;&gt;check_fn_body_and_added -->
<g id="edge55" class="edge">
<title>check_get_body_error-&gt;check_fn_body_and_added</title>
<path fill="none" stroke="black" d="M771.61,-1150C815.12,-1118.38 910.67,-1048.97 961.19,-1012.26"/>
<polygon fill="black" stroke="black" points="963.11,-1015.19 969.14,-1006.48 959,-1009.53 963.11,-1015.19"/>
</g>
<!-- log_get_body_error -->
<g id="node51" class="node">
<title>log_get_body_error</title>
<ellipse fill="none" stroke="black" cx="645" cy="-569.42" rx="97.95" ry="18"/>
<text text-anchor="middle" x="645" y="-565.22" font-family="Times,serif" font-size="14.00">记录错误并继续下一次循环</text>
</g>
<!-- check_get_body_error&#45;&gt;log_get_body_error -->
<g id="edge54" class="edge">
<title>check_get_body_error-&gt;log_get_body_error</title>
<path fill="none" stroke="black" d="M746,-1149.23C731.46,-1065.88 668.24,-703.62 649.97,-598.88"/>
<polygon fill="black" stroke="black" points="653.44,-598.42 648.27,-589.17 646.54,-599.62 653.44,-598.42"/>
</g>
<!-- log_get_body_error&#45;&gt;end_process_chunks_loop -->
<g id="edge56" class="edge">
<title>log_get_body_error-&gt;end_process_chunks_loop</title>
<path fill="none" stroke="black" d="M635.59,-551.27C617.5,-518.32 577.74,-445.91 555.97,-406.26"/>
<polygon fill="black" stroke="black" points="559.08,-404.65 551.19,-397.57 552.94,-408.02 559.08,-404.65"/>
</g>
<!-- skip_to_next_chunk&#45;&gt;end_process_chunks_loop -->
<g id="edge59" class="edge">
<title>skip_to_next_chunk-&gt;end_process_chunks_loop</title>
<path fill="none" stroke="black" d="M859.91,-765.74C854.05,-723.21 834.11,-612.12 779,-540 728.17,-473.48 641.71,-424.99 588.17,-399.47"/>
<polygon fill="black" stroke="black" points="589.84,-396.39 579.3,-395.32 586.87,-402.73 589.84,-396.39"/>
</g>
<!-- call_reply_for_cr -->
<g id="node54" class="node">
<title>call_reply_for_cr</title>
<ellipse fill="none" stroke="black" cx="1192" cy="-569.42" rx="231.25" ry="29.42"/>
<text text-anchor="middle" x="1192" y="-573.62" font-family="Times,serif" font-size="14.00">调用 GitLab API 添加评论 (p.ReplyForCr)</text>
<text text-anchor="middle" x="1192" y="-556.82" font-family="Times,serif" font-size="14.00">- 输入: path, evaluation, SHAs, dc, git, projectID, mrIID</text>
</g>
<!-- call_ai_cr&#45;&gt;call_reply_for_cr -->
<g id="edge60" class="edge">
<title>call_ai_cr-&gt;call_reply_for_cr</title>
<path fill="none" stroke="black" d="M1189.57,-742.37C1190.11,-704.32 1190.91,-647.9 1191.44,-610.18"/>
<polygon fill="black" stroke="black" points="1194.93,-610.68 1191.57,-600.63 1187.93,-610.58 1194.93,-610.68"/>
</g>
<!-- call_reply_for_cr&#45;&gt;end_process_chunks_loop -->
<g id="edge61" class="edge">
<title>call_reply_for_cr-&gt;end_process_chunks_loop</title>
<path fill="none" stroke="black" d="M1101.14,-541.98C966.12,-502.69 716.16,-429.96 601.43,-396.58"/>
<polygon fill="black" stroke="black" points="602.46,-393.24 591.88,-393.8 600.5,-399.96 602.46,-393.24"/>
</g>
</g>
</svg>
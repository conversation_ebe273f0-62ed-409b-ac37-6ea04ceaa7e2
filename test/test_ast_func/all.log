API server listening at: 127.0.0.1:57158
debugserver-@(#)PROGRAM:LLDB  PROJECT:lldb-1500.0.22.8
 for arm64.
Got a connection, launched process /Users/<USER>/Library/Caches/JetBrains/GoLand2024.1/tmp/GoLand/___2go_build_test_ast_func (pid = 43798).
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:61
Function signature: func (x ApplicationChannel) Enum() *ApplicationChannel
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:67
Function signature: func (x ApplicationChannel) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:71
Function signature: func (ApplicationChannel) Descriptor() protoreflect.EnumDescriptor
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:75
Function signature: func (ApplicationChannel) Type() protoreflect.EnumType
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:79
Function signature: func (x ApplicationChannel) Number() protoreflect.EnumNumber
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:84
Function signature: func (ApplicationChannel) EnumDescriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:96
Function signature: func (x *TestRabbitRes) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:105
Function signature: func (x *TestRabbitRes) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:109
Function signature: func (*TestRabbitRes) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:111
Function signature: func (x *TestRabbitRes) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:124
Function signature: func (*TestRabbitRes) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:128
Function signature: func (x *TestRabbitRes) GetData() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:143
Function signature: func (x *TestRabbitReply) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:152
Function signature: func (x *TestRabbitReply) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:156
Function signature: func (*TestRabbitReply) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:158
Function signature: func (x *TestRabbitReply) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:171
Function signature: func (*TestRabbitReply) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:175
Function signature: func (x *TestRabbitReply) GetData() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:190
Function signature: func (x *GetUserReq) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:199
Function signature: func (x *GetUserReq) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:203
Function signature: func (*GetUserReq) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:205
Function signature: func (x *GetUserReq) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:218
Function signature: func (*GetUserReq) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:222
Function signature: func (x *GetUserReq) GetUid() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:238
Function signature: func (x *GetUserResp) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:247
Function signature: func (x *GetUserResp) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:251
Function signature: func (*GetUserResp) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:253
Function signature: func (x *GetUserResp) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:266
Function signature: func (*GetUserResp) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:270
Function signature: func (x *GetUserResp) GetCode() uint32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:277
Function signature: func (x *GetUserResp) GetData() *Data
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:292
Function signature: func (x *Data) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:301
Function signature: func (x *Data) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:305
Function signature: func (*Data) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:307
Function signature: func (x *Data) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:320
Function signature: func (*Data) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:324
Function signature: func (x *Data) GetUserInfo() *UserInfo
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:341
Function signature: func (x *UserInfo) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:350
Function signature: func (x *UserInfo) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:354
Function signature: func (*UserInfo) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:356
Function signature: func (x *UserInfo) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:369
Function signature: func (*UserInfo) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:373
Function signature: func (x *UserInfo) GetUserID() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:380
Function signature: func (x *UserInfo) GetSchoolYear() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:387
Function signature: func (x *UserInfo) GetCreateTime() *timestamppb.Timestamp
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:404
Function signature: func (x *ListCateReq) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:413
Function signature: func (x *ListCateReq) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:417
Function signature: func (*ListCateReq) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:419
Function signature: func (x *ListCateReq) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:432
Function signature: func (*ListCateReq) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:436
Function signature: func (x *ListCateReq) GetName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:451
Function signature: func (x *ListCateReply) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:460
Function signature: func (x *ListCateReply) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:464
Function signature: func (*ListCateReply) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:466
Function signature: func (x *ListCateReply) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:479
Function signature: func (*ListCateReply) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:483
Function signature: func (x *ListCateReply) GetData() []*Category
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:504
Function signature: func (x *Category) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:513
Function signature: func (x *Category) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:517
Function signature: func (*Category) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:519
Function signature: func (x *Category) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:532
Function signature: func (*Category) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:536
Function signature: func (x *Category) GetId() int64
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:543
Function signature: func (x *Category) GetPositionType() uint32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:550
Function signature: func (x *Category) GetCreateUserId() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:557
Function signature: func (x *Category) GetCreateTime() *timestamppb.Timestamp
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:564
Function signature: func (x *Category) GetUpdateUserId() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:571
Function signature: func (x *Category) GetUpdateTime() *timestamppb.Timestamp
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:578
Function signature: func (x *Category) GetCategoryName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:606
Function signature: func (x *TestComplexDataReq) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:615
Function signature: func (x *TestComplexDataReq) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:619
Function signature: func (*TestComplexDataReq) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:621
Function signature: func (x *TestComplexDataReq) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:634
Function signature: func (*TestComplexDataReq) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:638
Function signature: func (x *TestComplexDataReq) GetName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:645
Function signature: func (x *TestComplexDataReq) GetAge() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:652
Function signature: func (x *TestComplexDataReq) GetTags() []string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:659
Function signature: func (x *TestComplexDataReq) GetData() *ListCateReq
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:666
Function signature: func (x *TestComplexDataReq) GetCreateTime() *timestamppb.Timestamp
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:673
Function signature: func (x *TestComplexDataReq) GetChannel() ApplicationChannel
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:680
Function signature: func (x *TestComplexDataReq) GetMap() *structpb.Struct
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:708
Function signature: func (x *TestComplexDataReply) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:717
Function signature: func (x *TestComplexDataReply) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:721
Function signature: func (*TestComplexDataReply) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:723
Function signature: func (x *TestComplexDataReply) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:736
Function signature: func (*TestComplexDataReply) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:740
Function signature: func (x *TestComplexDataReply) GetAge() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:747
Function signature: func (x *TestComplexDataReply) GetAge2() int64
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:754
Function signature: func (x *TestComplexDataReply) GetTags() []string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:761
Function signature: func (x *TestComplexDataReply) GetData() *ListCateReq
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:768
Function signature: func (x *TestComplexDataReply) GetCreateTime() *timestamppb.Timestamp
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:775
Function signature: func (x *TestComplexDataReply) GetChannel() ApplicationChannel
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:782
Function signature: func (x *TestComplexDataReply) GetMap() *structpb.Struct
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:797
Function signature: func (x *GrpcExampleReply) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:806
Function signature: func (x *GrpcExampleReply) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:810
Function signature: func (*GrpcExampleReply) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:812
Function signature: func (x *GrpcExampleReply) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:825
Function signature: func (*GrpcExampleReply) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:829
Function signature: func (x *GrpcExampleReply) GetName() []string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:858
Function signature: func (x *ConsumerRegion) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:867
Function signature: func (x *ConsumerRegion) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:871
Function signature: func (*ConsumerRegion) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:873
Function signature: func (x *ConsumerRegion) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:886
Function signature: func (*ConsumerRegion) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:890
Function signature: func (x *ConsumerRegion) GetAction() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:897
Function signature: func (x *ConsumerRegion) GetProvince() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:904
Function signature: func (x *ConsumerRegion) GetCity() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:911
Function signature: func (x *ConsumerRegion) GetDistrict() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:918
Function signature: func (x *ConsumerRegion) GetOldRegionCode() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:925
Function signature: func (x *ConsumerRegion) GetRegionCode() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:932
Function signature: func (x *ConsumerRegion) GetUserId() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:955
Function signature: func (x *ConsumerUsercore) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:964
Function signature: func (x *ConsumerUsercore) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:968
Function signature: func (*ConsumerUsercore) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:970
Function signature: func (x *ConsumerUsercore) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:983
Function signature: func (*ConsumerUsercore) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:987
Function signature: func (x *ConsumerUsercore) GetAction() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:994
Function signature: func (x *ConsumerUsercore) GetFields() []string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1001
Function signature: func (x *ConsumerUsercore) GetData() *ConsumerUsercore_Data
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1008
Function signature: func (x *ConsumerUsercore) GetNewUser() *ConsumerUsercore_Data
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1027
Function signature: func (x *MQUser) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1036
Function signature: func (x *MQUser) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1040
Function signature: func (*MQUser) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1042
Function signature: func (x *MQUser) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1055
Function signature: func (*MQUser) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1059
Function signature: func (x *MQUser) GetUserId() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1066
Function signature: func (x *MQUser) GetMessage() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1084
Function signature: func (x *ConsumerUsercore_Data) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1093
Function signature: func (x *ConsumerUsercore_Data) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1097
Function signature: func (*ConsumerUsercore_Data) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1099
Function signature: func (x *ConsumerUsercore_Data) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1112
Function signature: func (*ConsumerUsercore_Data) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1116
Function signature: func (x *ConsumerUsercore_Data) GetId() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1123
Function signature: func (x *ConsumerUsercore_Data) GetSchoolId() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1345
Function signature: func file_api_kratos_demo_api_proto_rawDescGZIP() []byte
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1410
Function signature: func init()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.go:1411
Function signature: func file_api_kratos_demo_api_proto_init()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:41
Function signature: func (m *TestRabbitRes) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:49
Function signature: func (m *TestRabbitRes) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:53
Function signature: func (m *TestRabbitRes) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:75
Function signature: func (m TestRabbitResMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:84
Function signature: func (m TestRabbitResMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:96
Function signature: func (e TestRabbitResValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:99
Function signature: func (e TestRabbitResValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:102
Function signature: func (e TestRabbitResValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:105
Function signature: func (e TestRabbitResValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:108
Function signature: func (e TestRabbitResValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:111
Function signature: func (e TestRabbitResValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:143
Function signature: func (m *TestRabbitReply) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:151
Function signature: func (m *TestRabbitReply) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:155
Function signature: func (m *TestRabbitReply) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:177
Function signature: func (m TestRabbitReplyMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:186
Function signature: func (m TestRabbitReplyMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:198
Function signature: func (e TestRabbitReplyValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:201
Function signature: func (e TestRabbitReplyValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:204
Function signature: func (e TestRabbitReplyValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:207
Function signature: func (e TestRabbitReplyValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:210
Function signature: func (e TestRabbitReplyValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:213
Function signature: func (e TestRabbitReplyValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:245
Function signature: func (m *GetUserReq) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:253
Function signature: func (m *GetUserReq) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:257
Function signature: func (m *GetUserReq) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:288
Function signature: func (m GetUserReqMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:297
Function signature: func (m GetUserReqMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:309
Function signature: func (e GetUserReqValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:312
Function signature: func (e GetUserReqValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:315
Function signature: func (e GetUserReqValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:318
Function signature: func (e GetUserReqValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:321
Function signature: func (e GetUserReqValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:324
Function signature: func (e GetUserReqValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:356
Function signature: func (m *GetUserResp) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:364
Function signature: func (m *GetUserResp) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:368
Function signature: func (m *GetUserResp) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:418
Function signature: func (m GetUserRespMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:427
Function signature: func (m GetUserRespMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:439
Function signature: func (e GetUserRespValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:442
Function signature: func (e GetUserRespValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:445
Function signature: func (e GetUserRespValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:448
Function signature: func (e GetUserRespValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:451
Function signature: func (e GetUserRespValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:454
Function signature: func (e GetUserRespValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:486
Function signature: func (m *Data) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:493
Function signature: func (m *Data) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:497
Function signature: func (m *Data) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:545
Function signature: func (m DataMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:554
Function signature: func (m DataMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:566
Function signature: func (e DataValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:569
Function signature: func (e DataValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:572
Function signature: func (e DataValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:575
Function signature: func (e DataValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:578
Function signature: func (e DataValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:581
Function signature: func (e DataValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:613
Function signature: func (m *UserInfo) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:621
Function signature: func (m *UserInfo) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:625
Function signature: func (m *UserInfo) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:677
Function signature: func (m UserInfoMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:686
Function signature: func (m UserInfoMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:698
Function signature: func (e UserInfoValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:701
Function signature: func (e UserInfoValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:704
Function signature: func (e UserInfoValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:707
Function signature: func (e UserInfoValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:710
Function signature: func (e UserInfoValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:713
Function signature: func (e UserInfoValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:745
Function signature: func (m *ListCateReq) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:753
Function signature: func (m *ListCateReq) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:757
Function signature: func (m *ListCateReq) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:778
Function signature: func (m ListCateReqMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:787
Function signature: func (m ListCateReqMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:799
Function signature: func (e ListCateReqValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:802
Function signature: func (e ListCateReqValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:805
Function signature: func (e ListCateReqValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:808
Function signature: func (e ListCateReqValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:811
Function signature: func (e ListCateReqValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:814
Function signature: func (e ListCateReqValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:846
Function signature: func (m *ListCateReply) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:854
Function signature: func (m *ListCateReply) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:858
Function signature: func (m *ListCateReply) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:912
Function signature: func (m ListCateReplyMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:921
Function signature: func (m ListCateReplyMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:933
Function signature: func (e ListCateReplyValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:936
Function signature: func (e ListCateReplyValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:939
Function signature: func (e ListCateReplyValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:942
Function signature: func (e ListCateReplyValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:945
Function signature: func (e ListCateReplyValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:948
Function signature: func (e ListCateReplyValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:980
Function signature: func (m *Category) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:988
Function signature: func (m *Category) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:992
Function signature: func (m *Category) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1079
Function signature: func (m CategoryMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1088
Function signature: func (m CategoryMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1100
Function signature: func (e CategoryValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1103
Function signature: func (e CategoryValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1106
Function signature: func (e CategoryValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1109
Function signature: func (e CategoryValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1112
Function signature: func (e CategoryValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1115
Function signature: func (e CategoryValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1147
Function signature: func (m *TestComplexDataReq) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1155
Function signature: func (m *TestComplexDataReq) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1159
Function signature: func (m *TestComplexDataReq) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1272
Function signature: func (m TestComplexDataReqMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1281
Function signature: func (m TestComplexDataReqMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1293
Function signature: func (e TestComplexDataReqValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1296
Function signature: func (e TestComplexDataReqValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1299
Function signature: func (e TestComplexDataReqValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1302
Function signature: func (e TestComplexDataReqValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1305
Function signature: func (e TestComplexDataReqValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1310
Function signature: func (e TestComplexDataReqValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1342
Function signature: func (m *TestComplexDataReply) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1350
Function signature: func (m *TestComplexDataReply) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1354
Function signature: func (m *TestComplexDataReply) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1467
Function signature: func (m TestComplexDataReplyMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1476
Function signature: func (m TestComplexDataReplyMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1488
Function signature: func (e TestComplexDataReplyValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1491
Function signature: func (e TestComplexDataReplyValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1494
Function signature: func (e TestComplexDataReplyValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1497
Function signature: func (e TestComplexDataReplyValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1500
Function signature: func (e TestComplexDataReplyValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1505
Function signature: func (e TestComplexDataReplyValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1537
Function signature: func (m *GrpcExampleReply) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1545
Function signature: func (m *GrpcExampleReply) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1549
Function signature: func (m *GrpcExampleReply) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1569
Function signature: func (m GrpcExampleReplyMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1578
Function signature: func (m GrpcExampleReplyMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1590
Function signature: func (e GrpcExampleReplyValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1593
Function signature: func (e GrpcExampleReplyValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1596
Function signature: func (e GrpcExampleReplyValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1599
Function signature: func (e GrpcExampleReplyValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1602
Function signature: func (e GrpcExampleReplyValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1605
Function signature: func (e GrpcExampleReplyValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1637
Function signature: func (m *ConsumerRegion) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1645
Function signature: func (m *ConsumerRegion) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1649
Function signature: func (m *ConsumerRegion) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1683
Function signature: func (m ConsumerRegionMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1692
Function signature: func (m ConsumerRegionMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1704
Function signature: func (e ConsumerRegionValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1707
Function signature: func (e ConsumerRegionValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1710
Function signature: func (e ConsumerRegionValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1713
Function signature: func (e ConsumerRegionValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1716
Function signature: func (e ConsumerRegionValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1719
Function signature: func (e ConsumerRegionValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1751
Function signature: func (m *ConsumerUsercore) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1759
Function signature: func (m *ConsumerUsercore) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1763
Function signature: func (m *ConsumerUsercore) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1843
Function signature: func (m ConsumerUsercoreMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1852
Function signature: func (m ConsumerUsercoreMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1864
Function signature: func (e ConsumerUsercoreValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1867
Function signature: func (e ConsumerUsercoreValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1870
Function signature: func (e ConsumerUsercoreValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1873
Function signature: func (e ConsumerUsercoreValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1876
Function signature: func (e ConsumerUsercoreValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1879
Function signature: func (e ConsumerUsercoreValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1911
Function signature: func (m *MQUser) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1918
Function signature: func (m *MQUser) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1922
Function signature: func (m *MQUser) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1964
Function signature: func (m MQUserMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1973
Function signature: func (m MQUserMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1985
Function signature: func (e MQUserValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1988
Function signature: func (e MQUserValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1991
Function signature: func (e MQUserValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1994
Function signature: func (e MQUserValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:1997
Function signature: func (e MQUserValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2000
Function signature: func (e MQUserValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2032
Function signature: func (m *ConsumerUsercore_Data) Validate() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2040
Function signature: func (m *ConsumerUsercore_Data) ValidateAll() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2044
Function signature: func (m *ConsumerUsercore_Data) validate(all bool) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2068
Function signature: func (m ConsumerUsercore_DataMultiError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2077
Function signature: func (m ConsumerUsercore_DataMultiError) AllErrors() []error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2089
Function signature: func (e ConsumerUsercore_DataValidationError) Field() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2092
Function signature: func (e ConsumerUsercore_DataValidationError) Reason() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2095
Function signature: func (e ConsumerUsercore_DataValidationError) Cause() error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2098
Function signature: func (e ConsumerUsercore_DataValidationError) Key() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2101
Function signature: func (e ConsumerUsercore_DataValidationError) ErrorName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api.pb.validate.go:2106
Function signature: func (e ConsumerUsercore_DataValidationError) Error() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:40
Function signature: func NewUserClient(cc grpc.ClientConnInterface) UserClient
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:44
Function signature: func (c *userClient) GetUser(ctx context.Context, in *GetUserReq, opts ...grpc.CallOption) (*GetUserResp, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:53
Function signature: func (c *userClient) TestRabbitProducer(ctx context.Context, in *TestRabbitRes, opts ...grpc.CallOption) (*TestRabbitReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:62
Function signature: func (c *userClient) TestComplexData(ctx context.Context, in *TestComplexDataReq, opts ...grpc.CallOption) (*TestComplexDataReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:71
Function signature: func (c *userClient) GrpcExample(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GrpcExampleReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:99
Function signature: func (UnimplementedUserServer) GetUser(context.Context, *GetUserReq) (*GetUserResp, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:102
Function signature: func (UnimplementedUserServer) TestRabbitProducer(context.Context, *TestRabbitRes) (*TestRabbitReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:105
Function signature: func (UnimplementedUserServer) TestComplexData(context.Context, *TestComplexDataReq) (*TestComplexDataReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:108
Function signature: func (UnimplementedUserServer) GrpcExample(context.Context, *emptypb.Empty) (*GrpcExampleReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:111
Function signature: func (UnimplementedUserServer) mustEmbedUnimplementedUserServer()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:120
Function signature: func RegisterUserServer(s grpc.ServiceRegistrar, srv UserServer)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:124
Function signature: func _User_GetUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:142
Function signature: func _User_TestRabbitProducer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:160
Function signature: func _User_TestComplexData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:178
Function signature: func _User_GrpcExample_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:235
Function signature: func NewKDemo2Client(cc grpc.ClientConnInterface) KDemo2Client
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:239
Function signature: func (c *kDemo2Client) ListCate(ctx context.Context, in *ListCateReq, opts ...grpc.CallOption) (*ListCateReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:260
Function signature: func (UnimplementedKDemo2Server) ListCate(context.Context, *ListCateReq) (*ListCateReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:263
Function signature: func (UnimplementedKDemo2Server) mustEmbedUnimplementedKDemo2Server()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:272
Function signature: func RegisterKDemo2Server(s grpc.ServiceRegistrar, srv KDemo2Server)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_grpc.pb.go:276
Function signature: func _KDemo2_ListCate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:35
Function signature: func RegisterUserHTTPServer(s *http.Server, srv UserHTTPServer)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:43
Function signature: func _User_GetUser0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:62
Function signature: func _User_TestRabbitProducer0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:81
Function signature: func _User_TestComplexData0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:100
Function signature: func _User_GrpcExample0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:130
Function signature: func NewUserHTTPClient(client *http.Client) UserHTTPClient
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:134
Function signature: func (c *UserHTTPClientImpl) GetUser(ctx context.Context, in *GetUserReq, opts ...http.CallOption) (*GetUserResp, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:147
Function signature: func (c *UserHTTPClientImpl) GrpcExample(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*GrpcExampleReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:160
Function signature: func (c *UserHTTPClientImpl) TestComplexData(ctx context.Context, in *TestComplexDataReq, opts ...http.CallOption) (*TestComplexDataReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:173
Function signature: func (c *UserHTTPClientImpl) TestRabbitProducer(ctx context.Context, in *TestRabbitRes, opts ...http.CallOption) (*TestRabbitReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:192
Function signature: func RegisterKDemo2HTTPServer(s *http.Server, srv KDemo2HTTPServer)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:197
Function signature: func _KDemo2_ListCate0_HTTP_Handler(srv KDemo2HTTPServer) func(ctx http.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:224
Function signature: func NewKDemo2HTTPClient(client *http.Client) KDemo2HTTPClient
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/api_http.pb.go:228
Function signature: func (c *KDemo2HTTPClientImpl) ListCate(ctx context.Context, in *ListCateReq, opts ...http.CallOption) (*ListCateReply, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason.pb.go:76
Function signature: func (x ErrorReason) Enum() *ErrorReason
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason.pb.go:82
Function signature: func (x ErrorReason) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason.pb.go:86
Function signature: func (ErrorReason) Descriptor() protoreflect.EnumDescriptor
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason.pb.go:90
Function signature: func (ErrorReason) Type() protoreflect.EnumType
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason.pb.go:94
Function signature: func (x ErrorReason) Number() protoreflect.EnumNumber
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason.pb.go:99
Function signature: func (ErrorReason) EnumDescriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason.pb.go:149
Function signature: func file_api_kratos_demo_error_reason_proto_rawDescGZIP() []byte
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason.pb.go:168
Function signature: func init()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason.pb.go:169
Function signature: func file_api_kratos_demo_error_reason_proto_init()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:15
Function signature: func IsErrorReasonUserNotFoundUnspecified(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:24
Function signature: func ErrorErrorReasonUserNotFoundUnspecified(format string, args ...interface{}) *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:29
Function signature: func ErrorMessageErrorReasonUserNotFoundUnspecified() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:33
Function signature: func IsErrorReasonContentMissing(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:41
Function signature: func ErrorErrorReasonContentMissing(format string, args ...interface{}) *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:45
Function signature: func ErrorMessageErrorReasonContentMissing() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:50
Function signature: func IsErrorReasonFeedbackFrequently(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:59
Function signature: func ErrorErrorReasonFeedbackFrequently(format string, args ...interface{}) *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:64
Function signature: func ErrorMessageErrorReasonFeedbackFrequently() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:69
Function signature: func IsErrorReasonDbFail(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:78
Function signature: func ErrorErrorReasonDbFail(format string, args ...interface{}) *errors.Error

/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:83
Function signature: func ErrorMessageErrorReasonDbFail() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:88
Function signature: func IsErrorReasonCbApiErr(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:97
Function signature: func ErrorErrorReasonCbApiErr(format string, args ...interface{}) *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:102
Function signature: func ErrorMessageErrorReasonCbApiErr() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:107
Function signature: func IsErrorReasonUsercoreApiErr(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:116
Function signature: func ErrorErrorReasonUsercoreApiErr(format string, args ...interface{}) *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:121
Function signature: func ErrorMessageErrorReasonUsercoreApiErr() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:126
Function signature: func IsErrorReasonStudystatusApiErr(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:135
Function signature: func ErrorErrorReasonStudystatusApiErr(format string, args ...interface{}) *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:140
Function signature: func ErrorMessageErrorReasonStudystatusApiErr() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:145
Function signature: func IsErrorReasonNotUniqueErr(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:154
Function signature: func ErrorErrorReasonNotUniqueErr(format string, args ...interface{}) *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:159
Function signature: func ErrorMessageErrorReasonNotUniqueErr() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:164
Function signature: func IsErrorReasonValidateErr(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:173
Function signature: func ErrorErrorReasonValidateErr(format string, args ...interface{}) *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:178
Function signature: func ErrorMessageErrorReasonValidateErr() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:183
Function signature: func IsServerPanic(err error) bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:192
Function signature: func ErrorServerPanic(format string, args ...interface{}) *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/api/kratos_demo/error_reason_errors.pb.go:197
Function signature: func ErrorMessageServerPanic() *errors.Error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo/main.go:38
Function signature: func Init()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo/main.go:42
Function signature: func newApp(c *conf.Bootstrap, namingClient naming_client.INamingClient, logger log.Logger, hs *http.Server, gs *grpc.Server, kafkaConsumerServer *server.KafkaConsumer, rabbitConsumerServer *server.RabbitConsumerServer, cronServer *server.Cron) *kratos.App
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo/main.go:72
Function signature: func main()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo/wire.go:21
Function signature: func initApp(*conf.Bootstrap, *conf.PGGroup, *conf.RedisGroup, *conf.Server, log.Logger, *conf.Kafkas, *conf.RabbitMQGroup, *conf.CronTasks) (*kratos.App, func(), error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo/wire_gen.go:27
Function signature: func initApp(bootstrap *conf.Bootstrap, pgGroup *conf.PGGroup, redisGroup *conf.RedisGroup, confServer *conf.Server, logger log.Logger, kafkas *conf.Kafkas, rabbitMQGroup *conf.RabbitMQGroup, cronTasks *conf.CronTasks) (*kratos.App, func(), error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo-cli/main.go:34
Function signature: func Init()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo-cli/main.go:38
Function signature: func newApp(c *conf.Bootstrap, namingClient naming_client.INamingClient, logger log.Logger, cli *server.Cli) *kratos.App
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo-cli/main.go:56
Function signature: func main()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo-cli/wire.go:21
Function signature: func initApp(*conf.Bootstrap, *conf.PGGroup, *conf.RedisGroup, *conf.Server, log.Logger, *conf.Kafkas, *conf.RabbitMQGroup) (*kratos.App, func(), error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/cmd/kratos-demo-cli/wire_gen.go:27
Function signature: func initApp(bootstrap *conf.Bootstrap, pgGroup *conf.PGGroup, redisGroup *conf.RedisGroup, confServer *conf.Server, logger log.Logger, kafkas *conf.Kafkas, rabbitMQGroup *conf.RabbitMQGroup) (*kratos.App, func(), error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/biz/biz_user.go:25
Function signature: func NewUserUsecase(logger log.Logger, data Transaction, userRepo UserRepo, config *conf.Bootstrap) *UserUsecase
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/biz/biz_user.go:34
Function signature: func (u *UserUsecase) GetUser(ctx context.Context, userID string) (user *User, err error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/biz/biz_user.go:58
Function signature: func (u *UserUsecase) TestRabbitProducer(message string) string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/biz/biz_user.go:68
Function signature: func (u *UserUsecase) TestGrpcReq(ctx context.Context) ([]string, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:50
Function signature: func (x GO_ENV) Enum() *GO_ENV
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:56
Function signature: func (x GO_ENV) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:60
Function signature: func (GO_ENV) Descriptor() protoreflect.EnumDescriptor
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:64
Function signature: func (GO_ENV) Type() protoreflect.EnumType
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:68
Function signature: func (x GO_ENV) Number() protoreflect.EnumNumber
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:73
Function signature: func (GO_ENV) EnumDescriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:101
Function signature: func (x CronTasks_NOTIFY) Enum() *CronTasks_NOTIFY
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:107
Function signature: func (x CronTasks_NOTIFY) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:111
Function signature: func (CronTasks_NOTIFY) Descriptor() protoreflect.EnumDescriptor
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:115
Function signature: func (CronTasks_NOTIFY) Type() protoreflect.EnumType
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:119
Function signature: func (x CronTasks_NOTIFY) Number() protoreflect.EnumNumber
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:124
Function signature: func (CronTasks_NOTIFY) EnumDescriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:143
Function signature: func (x *Bootstrap) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:152
Function signature: func (x *Bootstrap) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:156
Function signature: func (*Bootstrap) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:158
Function signature: func (x *Bootstrap) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:171
Function signature: func (*Bootstrap) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:175
Function signature: func (x *Bootstrap) GetName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:182
Function signature: func (x *Bootstrap) GetServer() *Server
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:189
Function signature: func (x *Bootstrap) GetEnv() GO_ENV
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:196
Function signature: func (x *Bootstrap) GetYc() *Bootstrap_YC
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:203
Function signature: func (x *Bootstrap) GetLogger() *Logger
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:210
Function signature: func (x *Bootstrap) GetYapiProjectId() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:217
Function signature: func (x *Bootstrap) GetGrpcService() *GrpcService
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:224
Function signature: func (x *Bootstrap) GetCtxHeaders() []string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:240
Function signature: func (x *Server) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:249
Function signature: func (x *Server) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:253
Function signature: func (*Server) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:255
Function signature: func (x *Server) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:268
Function signature: func (*Server) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:272
Function signature: func (x *Server) GetHttp() *Server_HTTP
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:279
Function signature: func (x *Server) GetGrpc() *Server_GRPC
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:324
Function signature: func (x *Redis) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:333
Function signature: func (x *Redis) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:337
Function signature: func (*Redis) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:339
Function signature: func (x *Redis) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:352
Function signature: func (*Redis) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:356
Function signature: func (x *Redis) GetAddr() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:363
Function signature: func (x *Redis) GetPassword() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:370
Function signature: func (x *Redis) GetDb() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:377
Function signature: func (x *Redis) GetName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:384
Function signature: func (x *Redis) GetUsername() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:391
Function signature: func (x *Redis) GetReadTimeout() int64
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:398
Function signature: func (x *Redis) GetWriteTimeout() int64
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:405
Function signature: func (x *Redis) GetKeyPrefix() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:412
Function signature: func (x *Redis) GetMaxLatency() int64
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:419
Function signature: func (x *Redis) GetPoolFIFO() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:426
Function signature: func (x *Redis) GetPoolSize() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:433
Function signature: func (x *Redis) GetPoolTimeout() int64
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:440
Function signature: func (x *Redis) GetMinIdleConns() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:447
Function signature: func (x *Redis) GetIdleTimeout() int64
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:454
Function signature: func (x *Redis) GetMaxConnAge() int64
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:461
Function signature: func (x *Redis) GetEnableTrace() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:500
Function signature: func (x *PG) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:509
Function signature: func (x *PG) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:513
Function signature: func (*PG) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:515
Function signature: func (x *PG) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:528
Function signature: func (*PG) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:532
Function signature: func (x *PG) GetHost() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:539
Function signature: func (x *PG) GetPort() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:546
Function signature: func (x *PG) GetUser() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:553
Function signature: func (x *PG) GetPassword() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:560
Function signature: func (x *PG) GetDb() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:567
Function signature: func (x *PG) GetSslMode() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:574
Function signature: func (x *PG) GetMaxIdleConns() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:581
Function signature: func (x *PG) GetMaxOpenConns() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:588
Function signature: func (x *PG) GetConnMaxLifetime() *durationpb.Duration
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:595
Function signature: func (x *PG) GetDebug() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:602
Function signature: func (x *PG) GetSingularTable() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:609
Function signature: func (x *PG) GetDisableMetric() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:616
Function signature: func (x *PG) GetSlowThreshold() *durationpb.Duration
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:623
Function signature: func (x *PG) GetUpdateColumn() []string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:630
Function signature: func (x *PG) GetName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:648
Function signature: func (x *Logger) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:657
Function signature: func (x *Logger) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:661
Function signature: func (*Logger) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:663
Function signature: func (x *Logger) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:676
Function signature: func (*Logger) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:680
Function signature: func (x *Logger) GetHeaderWhites() []string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:687
Function signature: func (x *Logger) GetEnableResWithCode() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:694
Function signature: func (x *Logger) GetEnableReqWithCode() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:712
Function signature: func (x *Nacos) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:721
Function signature: func (x *Nacos) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:725
Function signature: func (*Nacos) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:727
Function signature: func (x *Nacos) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:740
Function signature: func (*Nacos) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:744
Function signature: func (x *Nacos) GetHost() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:751
Function signature: func (x *Nacos) GetPort() uint64
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:758
Function signature: func (x *Nacos) GetUsername() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:765
Function signature: func (x *Nacos) GetPassword() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:798
Function signature: func (x *Kafka) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:807
Function signature: func (x *Kafka) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:811
Function signature: func (*Kafka) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:813
Function signature: func (x *Kafka) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:826
Function signature: func (*Kafka) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:830
Function signature: func (x *Kafka) GetName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:837
Function signature: func (x *Kafka) GetAddress() []string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:844
Function signature: func (x *Kafka) GetSecurityProtocol() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:851
Function signature: func (x *Kafka) GetCertFile() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:858
Function signature: func (x *Kafka) GetTls() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:865
Function signature: func (x *Kafka) GetTopic() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:872
Function signature: func (x *Kafka) GetSasl() *Kafka_Sasl
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:879
Function signature: func (x *Kafka) GetProducer() *Kafka_Producer
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:886
Function signature: func (x *Kafka) GetConsumer() *Kafka_Consumer
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:916
Function signature: func (x *RabbitMq) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:925
Function signature: func (x *RabbitMq) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:929
Function signature: func (*RabbitMq) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:931
Function signature: func (x *RabbitMq) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:944
Function signature: func (*RabbitMq) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:948
Function signature: func (x *RabbitMq) GetVhost() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:955
Function signature: func (x *RabbitMq) GetUserName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:962
Function signature: func (x *RabbitMq) GetPassword() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:969
Function signature: func (x *RabbitMq) GetEndPoint() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:976
Function signature: func (x *RabbitMq) GetReconnectLimit() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:983
Function signature: func (x *RabbitMq) GetReconnectTimeGap() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:990
Function signature: func (x *RabbitMq) GetProducer() *RabbitMq_Producer
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:997
Function signature: func (x *RabbitMq) GetConsumer() *RabbitMq_Consumer
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1015
Function signature: func (x *Kafkas) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1024
Function signature: func (x *Kafkas) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1028
Function signature: func (*Kafkas) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1030
Function signature: func (x *Kafkas) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1043
Function signature: func (*Kafkas) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1047
Function signature: func (x *Kafkas) GetUser() *Kafka
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1054
Function signature: func (x *Kafkas) GetUserRegion() *Kafka
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1069
Function signature: func (x *PGGroup) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1078
Function signature: func (x *PGGroup) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1082
Function signature: func (*PGGroup) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1084
Function signature: func (x *PGGroup) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1097
Function signature: func (*PGGroup) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1101
Function signature: func (x *PGGroup) GetDemo() *PG
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1116
Function signature: func (x *RedisGroup) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1125
Function signature: func (x *RedisGroup) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1129
Function signature: func (*RedisGroup) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1131
Function signature: func (x *RedisGroup) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1144
Function signature: func (*RedisGroup) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1148
Function signature: func (x *RedisGroup) GetDemo() *Redis
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1163
Function signature: func (x *RabbitMQGroup) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1172
Function signature: func (x *RabbitMQGroup) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1176
Function signature: func (*RabbitMQGroup) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1178
Function signature: func (x *RabbitMQGroup) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1191
Function signature: func (*RabbitMQGroup) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1195
Function signature: func (x *RabbitMQGroup) GetDemo() *RabbitMq
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1211
Function signature: func (x *GrpcService) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1220
Function signature: func (x *GrpcService) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1224
Function signature: func (*GrpcService) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1226
Function signature: func (x *GrpcService) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1239
Function signature: func (*GrpcService) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1243
Function signature: func (x *GrpcService) GetCourseBusiness() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1266
Function signature: func (x *CronTasks) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1275
Function signature: func (x *CronTasks) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1279
Function signature: func (*CronTasks) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1281
Function signature: func (x *CronTasks) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1294
Function signature: func (*CronTasks) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1298
Function signature: func (x *CronTasks) GetNotify() CronTasks_NOTIFY
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1305
Function signature: func (x *CronTasks) GetFeishu() *CronTasks_Feishu
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1312
Function signature: func (x *CronTasks) GetTask1() *CronTasks_CronTask
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1319
Function signature: func (x *CronTasks) GetTask2() *CronTasks_CronTask
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1339
Function signature: func (x *Bootstrap_YC) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1348
Function signature: func (x *Bootstrap_YC) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1352
Function signature: func (*Bootstrap_YC) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1354
Function signature: func (x *Bootstrap_YC) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1367
Function signature: func (*Bootstrap_YC) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1371
Function signature: func (x *Bootstrap_YC) GetRedis() *RedisGroup
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1378
Function signature: func (x *Bootstrap_YC) GetPg() *PGGroup
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1385
Function signature: func (x *Bootstrap_YC) GetNacos() *Nacos
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1392
Function signature: func (x *Bootstrap_YC) GetKafka() *Kafkas
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1399
Function signature: func (x *Bootstrap_YC) GetRabbitmq() *RabbitMQGroup
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1406
Function signature: func (x *Bootstrap_YC) GetCronTasks() *CronTasks
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1423
Function signature: func (x *Server_HTTP) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1432
Function signature: func (x *Server_HTTP) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1436
Function signature: func (*Server_HTTP) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1438
Function signature: func (x *Server_HTTP) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1451
Function signature: func (*Server_HTTP) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1455
Function signature: func (x *Server_HTTP) GetNetwork() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1462
Function signature: func (x *Server_HTTP) GetAddr() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1469
Function signature: func (x *Server_HTTP) GetTimeout() *durationpb.Duration
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1486
Function signature: func (x *Server_GRPC) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1495
Function signature: func (x *Server_GRPC) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1499
Function signature: func (*Server_GRPC) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1501
Function signature: func (x *Server_GRPC) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1514
Function signature: func (*Server_GRPC) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1518
Function signature: func (x *Server_GRPC) GetNetwork() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1525
Function signature: func (x *Server_GRPC) GetAddr() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1532
Function signature: func (x *Server_GRPC) GetTimeout() *durationpb.Duration
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1555
Function signature: func (x *Kafka_Sasl) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1564
Function signature: func (x *Kafka_Sasl) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1568
Function signature: func (*Kafka_Sasl) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1570
Function signature: func (x *Kafka_Sasl) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1583
Function signature: func (*Kafka_Sasl) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1587
Function signature: func (x *Kafka_Sasl) GetEnable() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1594
Function signature: func (x *Kafka_Sasl) GetUser() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1601
Function signature: func (x *Kafka_Sasl) GetPassword() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1608
Function signature: func (x *Kafka_Sasl) GetMechanism() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1631
Function signature: func (x *Kafka_Producer) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1640
Function signature: func (x *Kafka_Producer) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1644
Function signature: func (*Kafka_Producer) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1646
Function signature: func (x *Kafka_Producer) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1659
Function signature: func (*Kafka_Producer) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1663
Function signature: func (x *Kafka_Producer) GetBatch() bool
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1670
Function signature: func (x *Kafka_Producer) GetBatchMaxMs() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1677
Function signature: func (x *Kafka_Producer) GetBatchMaxMessages() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1684
Function signature: func (x *Kafka_Producer) GetBatchMaxKbytes() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1713
Function signature: func (x *Kafka_Consumer) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1722
Function signature: func (x *Kafka_Consumer) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1726
Function signature: func (*Kafka_Consumer) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1728
Function signature: func (x *Kafka_Consumer) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1741
Function signature: func (*Kafka_Consumer) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1745
Function signature: func (x *Kafka_Consumer) GetMode() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1752
Function signature: func (x *Kafka_Consumer) GetBatchAmount() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1759
Function signature: func (x *Kafka_Consumer) GetMaxWaitMs() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1766
Function signature: func (x *Kafka_Consumer) GetTimeoutMs() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1773
Function signature: func (x *Kafka_Consumer) GetGroup() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1780
Function signature: func (x *Kafka_Consumer) GetMaxPollMs() int32
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1787
Function signature: func (x *Kafka_Consumer) GetAutoOffsetReset() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1804
Function signature: func (x *RabbitMq_Producer) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1813
Function signature: func (x *RabbitMq_Producer) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1817
Function signature: func (*RabbitMq_Producer) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1819
Function signature: func (x *RabbitMq_Producer) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1832
Function signature: func (*RabbitMq_Producer) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1836
Function signature: func (x *RabbitMq_Producer) GetExchangeName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1843
Function signature: func (x *RabbitMq_Producer) GetQueueName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1861
Function signature: func (x *RabbitMq_Consumer) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1870
Function signature: func (x *RabbitMq_Consumer) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1874
Function signature: func (*RabbitMq_Consumer) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1876
Function signature: func (x *RabbitMq_Consumer) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1889
Function signature: func (*RabbitMq_Consumer) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1893
Function signature: func (x *RabbitMq_Consumer) GetExchangeName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1900
Function signature: func (x *RabbitMq_Consumer) GetQueueName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1907
Function signature: func (x *RabbitMq_Consumer) GetBindingKey() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1926
Function signature: func (x *CronTasks_Feishu) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1935
Function signature: func (x *CronTasks_Feishu) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1939
Function signature: func (*CronTasks_Feishu) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1941
Function signature: func (x *CronTasks_Feishu) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1954
Function signature: func (*CronTasks_Feishu) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1958
Function signature: func (x *CronTasks_Feishu) GetUrl() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1965
Function signature: func (x *CronTasks_Feishu) GetTitle() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1989
Function signature: func (x *CronTasks_CronTask) Reset()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:1998
Function signature: func (x *CronTasks_CronTask) String() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2002
Function signature: func (*CronTasks_CronTask) ProtoMessage()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2004
Function signature: func (x *CronTasks_CronTask) ProtoReflect() protoreflect.Message
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2017
Function signature: func (*CronTasks_CronTask) Descriptor() ([]byte, []int)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2021
Function signature: func (x *CronTasks_CronTask) GetName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2028
Function signature: func (x *CronTasks_CronTask) GetCron() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2035
Function signature: func (x *CronTasks_CronTask) GetMode() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2042
Function signature: func (x *CronTasks_CronTask) GetNotify() CronTasks_NOTIFY
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2049
Function signature: func (x *CronTasks_CronTask) GetFeishu() *CronTasks_Feishu
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2338
Function signature: func file_internal_conf_conf_proto_rawDescGZIP() []byte
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2417
Function signature: func init()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/conf/conf.pb.go:2418
Function signature: func file_internal_conf_conf_proto_init()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:70
Function signature: func newRabbitmq(config *conf.RabbitMq) (*rabbitmq.Producer, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:95
Function signature: func NewRabbitMQGroup(config *conf.RabbitMQGroup, l log.Logger) (*RabbitMQProducerGroup, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:105
Function signature: func NewData(dbGroup *DBGroup, l log.Logger) *Data
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:114
Function signature: func newDB(config *conf.PG, logger *log.Helper) (*gorm.DB, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:147
Function signature: func NewDBGroup(config *conf.PGGroup, l log.Logger) (*DBGroup, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:158
Function signature: func newRedis(config *conf.Redis, logger *log.Helper) redis.IClient
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:188
Function signature: func NewRedisGroup(config *conf.RedisGroup, l log.Logger) *RedisGroup
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:197
Function signature: func (d *Data) ExecTx(ctx context.Context, fn func(ctx context.Context) error) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:211
Function signature: func NewTransaction(d *Data) biz.Transaction
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:215
Function signature: func (d *Data) DB(ctx context.Context) *gorm.DB
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:224
Function signature: func Init(config *conf.Bootstrap) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:236
Function signature: func GetRequest(ctx context.Context) *onionms.AllMs
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:249
Function signature: func InitNacosNamingClient(config *conf.Bootstrap, l log.Logger) (naming_client.INamingClient, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:276
Function signature: func GetCourseBusiness(namingClient naming_client.INamingClient, config *conf.Bootstrap) (derivative.DerivativeClient, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:303
Function signature: func NewMQClient(kafkaConf *conf.Kafkas, l log.Logger) (*Producer, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data.go:324
Function signature: func NewRestyClient() *resty.Client
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:37
Function signature: func NewUserRepo(cache *RedisGroup, data *Data, logger log.Logger, config *conf.Bootstrap, rabbit *RabbitMQProducerGroup, kafka *Producer, courseBusiness derivative.DerivativeClient) biz.UserRepo
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:58
Function signature: func (u *userRepo) Detail(ctx context.Context, userID string) (*biz.User, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:63
Function signature: func (u *userRepo) detail(ctx context.Context, userID string) (*biz.User, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:75
Function signature: func (u *userRepo) RabbitProducer(message string) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:82
Function signature: func (u *userRepo) TestGrpcReq(ctx context.Context) ([]string, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:95
Function signature: func (u *userRepo) GetUser(ctx context.Context, userID string) (*biz.User, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:153
Function signature: func (u *userRepo) CreateUser(ctx context.Context, user *biz.User) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:171
Function signature: func (u *userRepo) PubMQ(ctx context.Context, userID string) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:186
Function signature: func (u *userRepo) UpdateUserGrade(ctx context.Context, userID string, schoolYear string) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/data_user.go:213
Function signature: func (u *userRepo) DeleteUser(ctx context.Context, userID string) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/cli.go:13
Function signature: func NewCli(cliService *service.CliService) *Cli
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/cli.go:20
Function signature: func (c *Cli) Start(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/cli.go:26
Function signature: func (c *Cli) Stop(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/consumer_kafka.go:22
Function signature: func (k *KafkaConsumer) Start(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/consumer_kafka.go:35
Function signature: func (k *KafkaConsumer) Stop(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/consumer_kafka.go:48
Function signature: func NewKafkaConsumerServer(config *conf.Bootstrap, c *conf.Kafkas, et *service.UserService, logger log.Logger) (*KafkaConsumer, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/consumer_kafka.go:63
Function signature: func getKafkaConsumer(c *conf.Kafka, process mq.Handler, middle ...mq.Middleware) (mq.Consumer, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/consumer_rabbit.go:27
Function signature: func NewRabbitConsumerServer(c *conf.RabbitMQGroup, ut *service.UserService, logger log.Logger) (*RabbitConsumerServer, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/consumer_rabbit.go:39
Function signature: func getRabbitConsumer(c *conf.RabbitMq) (*rabbitmq.Consumer, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/consumer_rabbit.go:61
Function signature: func (c *RabbitConsumerServer) Start(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/consumer_rabbit.go:68
Function signature: func (c *RabbitConsumerServer) Stop(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/cron.go:22
Function signature: func (k *Cron) Start(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/cron.go:35
Function signature: func (k *Cron) Stop(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/cron.go:48
Function signature: func NewCronServer(config *conf.Bootstrap, c *conf.CronTasks, et *service.UserService, logger log.Logger) (*Cron, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/grpc.go:26
Function signature: func NewGRPCServer(c *conf.Server, config *conf.Bootstrap, userService *service.UserService, logger log.Logger) *grpc.Server
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/http.go:36
Function signature: func customMiddleware(handler middleware.Handler) middleware.Handler
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/http.go:47
Function signature: func NewHTTPServer(config *conf.Bootstrap, c *conf.Server, userService *service.UserService, logger log.Logger) *http.Server
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/http.go:99
Function signature: func RegisterPprof(srv *http.Server)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/server/http.go:113
Function signature: func RegisterGinRouter(engine *gin.Engine)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/cli.go:17
Function signature: func NewCliService(logger log.Logger, user *biz.UserUsecase) *CliService
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/cli.go:21
Function signature: func (c *CliService) Run()
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:25
Function signature: func NewUserService(logger log.Logger, user *biz.UserUsecase, config *conf.Bootstrap) *UserService
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:29
Function signature: func (s *UserService) GetUser(ctx context.Context, req *pb.GetUserReq) (*pb.GetUserResp, error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:52
Function signature: func (s *UserService) TestRabbitProducer(ctx context.Context, req *pb.TestRabbitRes) (res *pb.TestRabbitReply, err error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:62
Function signature: func (s *UserService) TestRabbitConsumer(payload []byte, delivery *amqp091.Delivery, autoAck bool)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:76
Function signature: func (s *UserService) TestComplexData(ctx context.Context, req *pb.TestComplexDataReq) (reply *pb.TestComplexDataReply, err error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:89
Function signature: func (s *UserService) GrpcExample(ctx context.Context, req *emptypb.Empty) (reply *pb.GrpcExampleReply, err error)
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:100
Function signature: func (s *UserService) ConsumerUserCore(ctx context.Context, message mq.Message) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:120
Function signature: func (s *UserService) ConsumerUserRegion(ctx context.Context, message mq.Message) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:132
Function signature: func (s *UserService) CronTask1(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/service/user.go:139
Function signature: func (s *UserService) CronTask2(ctx context.Context) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/school.go:8
Function signature: func NewSchool() *School
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/school.go:26
Function signature: func (p School) TableName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/school.go:31
Function signature: func (p School) PKName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/school.go:36
Function signature: func (p School) PKField() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/school.go:41
Function signature: func (p School) PKType() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/school.go:46
Function signature: func (p School) GetColumnByKey() map[string]string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/usercore.go:8
Function signature: func NewUsercore() *Usercore
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/usercore.go:26
Function signature: func (p Usercore) TableName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/usercore.go:31
Function signature: func (p Usercore) PKName() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/usercore.go:36
Function signature: func (p Usercore) PKField() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/usercore.go:41
Function signature: func (p Usercore) PKType() string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/data/dao/usercore.go:46
Function signature: func (p Usercore) GetColumnByKey() map[string]string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/pkg/errcodec/client_encoder.go:19
Function signature: func ClientErrorDecoder(ctx context.Context, res *http2.Response) error
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/pkg/errcodec/server_encoder.go:23
Function signature: func ContentType(subtype string) string
/Users/<USER>/gocode/study_repo/api_count/kratos-demo/internal/pkg/errcodec/server_encoder.go:27
Function signature: func ServerErrorEncoder(w http.ResponseWriter, r *http.Request, err error)
Exiting.

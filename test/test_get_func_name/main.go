package main

import (
	"fmt"
	"gitlab_ai_cr/file_api"
)

func main() {

	//fset := token.NewFileSet()
	//
	//// 解析代码，生成 AST
	//fileNode, err := parser.ParseFile(fset, "", funcSig, parser.ParseComments)
	//if err != nil {
	//	fmt.Println("解析错误：", err)
	//	return
	//}
	//
	//ast.Print(fset, fileNode)

	funcSigs, _ := file_api.GetCompelteFunc(funcSig)
	if funcSigs != nil {
		fmt.Printf("函数名: %s\n", funcSigs.Name)
		fmt.Printf("接收者: %s\n", funcSigs.Receiver)
		fmt.Printf("参数列表: %v\n", funcSigs.Parameters)
		fmt.Printf("返回值列表: %v\n", funcSigs.Results)
	} else {
		fmt.Println("解析函数失败。")
	}

	return

	// fBody, err := file_api.ExtractFunctionBody(funcSig, fileContent)
	// if err != nil {
	// 	fmt.Println("Error extracting function body:", err)
	// 	return
	// }

	// fmt.Println(fBody)
	// _ = fBody
}

var funcSig = `func (t *totalReview) SpecialListPadSupport(c *gin.Context) {`
var fileContent = `
package handlers

import (
	"github.com/gin-gonic/gin" 
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	"gitlab.yc345.tv/backend/study-course/lib"
	"gitlab.yc345.tv/backend/study-course/services"
	"gitlab.yc345.tv/backend/study-course/services/pkg/transport"
	"gitlab.yc345.tv/backend/study-course/services/vo"
	"gitlab.yc345.tv/backend/study-course/setup"
	"gitlab.yc345.tv/backend/utils/v2/errgroup"
	"gitlab.yc345.tv/backend/yc/pkg/ecode"
	"gitlab.yc345.tv/backend/yc/pkg/valid"
	"net/http"
	"sync"
	"time"
)

var newTotalReview = &totalReview{valid: valid.New()}

// totalReview 总复习
type totalReview struct {
	valid *valid.Validator
}

// GetCourseList  获取总复习课程包列表
func (t *totalReview) GetCourseList(c *gin.Context) {
	userID := c.GetHeader(uid)
	sn := c.GetHeader("sn")
	clientType := c.GetHeader("client-type")
	clientVersion := c.GetHeader("client-version")
	clientCategory := c.GetHeader("client-category")
	ctx := c.Request.Context()
	res, err := services.Get(c).TotalReview.GetCourseList(ctx, userID, sn, clientType, clientVersion, clientCategory)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}

// TotalReviewRecommend 总复习模块 推荐逻辑
func (t *totalReview) TotalReviewRecommend(c *gin.Context) {
	userID := c.GetHeader(uid)
	sn := c.GetHeader("sn")
	clientType := c.GetHeader("client-type")
	clientVersion := c.GetHeader("client-version")
	clientCategory := c.GetHeader("client-category")

	stageId := c.Query("stageId")
	subjectId := c.Query("subjectId")

	if err := t.valid.CheckFields(
		t.valid.Check("uid", userIDCheck, userID),
		// t.valid.Check("stageId", "required,gt=0", cast.ToInt64(stageId)),
		// t.valid.Check("subjectId", "required,gt=0", cast.ToInt64(subjectId)),
	); err != nil {
		err.ParamsErr(c)
		return
	}

	ctx := c.Request.Context()
	res, err := services.Get(c).TotalReview.GetTotalReviewRecommend(ctx, userID, sn, clientType, clientVersion, clientCategory, stageId, subjectId)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}

// GetPackageChapter 获取课程包章节信息
func (t *totalReview) GetPackageChapter(c *gin.Context) {
	userID := c.GetHeader(uid)
	specialCourseId := c.Param("specialCourseId")
	if err := t.valid.CheckFields(
		t.valid.Check("uid", userIDCheck, userID),
		t.valid.Check("specialCourseId", "required,gt=0", specialCourseId)); err != nil {
		err.ParamsErr(c)
		return
	}
	ctx := c.Request.Context()
	res, _, err := services.Get(c).TotalReview.GetPackageChapter(ctx, userID, specialCourseId)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}

// GetPackageChapter 获取课程包章节信息
func (t *totalReview) GetPackageSimple(c *gin.Context) {
	specialCourseId := c.Query("specialCourseId")
	if err := t.valid.CheckFields(
		t.valid.Check("specialCourseId", "required,gt=0", specialCourseId)); err != nil {
		err.ParamsErr(c)
		return
	}
	ctx := c.Request.Context()
	res, err := services.Get(c).TotalReview.GetPackageSimple(ctx, specialCourseId)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}

// GetRegionMapList 获取地理位置列表
func (t *totalReview) GetRegionMapList(c *gin.Context) {
	userID := c.GetHeader(uid)
	sn := c.GetHeader("sn")
	clientType := c.GetHeader("client-type")
	clientVersion := c.GetHeader("client-version")
	clientCategory := c.GetHeader("client-category")

	stageId := c.Query("stageId")
	subjectId := c.Query("subjectId")

	if err := t.valid.CheckFields(
		t.valid.Check("uid", userIDCheck, userID),
		t.valid.Check("stageId", "required,gt=0", cast.ToInt64(stageId)),
		t.valid.Check("subjectId", "required,gt=0", cast.ToInt64(subjectId)),
	); err != nil {
		err.ParamsErr(c)
		return
	}

	ctx := c.Request.Context()
	res, err := services.Get(c).TotalReview.GetRegionMapList(ctx, userID, sn, clientType, clientVersion, clientCategory, stageId, subjectId)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}

// SpecialChapterDetail 专题课章节详情
func (t *totalReview) SpecialChapterDetail(c *gin.Context) {
	userID := c.GetHeader(uid)
	packageID := c.Query("packageId")
	chapterID := c.Query("chapterId")

	if err := t.valid.CheckFields(
		t.valid.Check("uid", userIDCheck, userID),
		t.valid.Check("packageId", "required", packageID),
		t.valid.Check("chapterId", "required", chapterID),
	); err != nil {
		err.ParamsErr(c)
		return
	}

	ctx := c.Request.Context()
	res, err := services.Get(c).SpecialCourse.GetChapterDetail(ctx, userID, packageID, chapterID)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}

// SpecialList 获取再售的专项课 & 用户已有的专项课.
func (t *totalReview) SpecialList(c *gin.Context) {
	userID := c.GetHeader(uid)
	if err := t.valid.CheckFields(
		t.valid.Check("uid", userIDCheck, userID),
	); err != nil {
		err.ParamsErr(c)
		return
	}
	currentCourseID := c.Query("courseId")
	// 场景值
	//scene := c.Query("scene")
	//needUserData := c.Query("")
	// 应用

	excludeType := c.Query("excludeType")
	includeType := c.Query("includeType")
	excludeTypes, includeTypes := lib.FilterTypes(excludeType, includeType)
	if excludeType == "" && includeType == "" {
		excludeTypes = append(excludeTypes, "yc_planet_learn_practice")
	}

	res, err := services.Get(c).SpecialCourse.SpecialList(transport.GinCtx2Ctx(c), userID, currentCourseID, excludeTypes, includeTypes)
	//2. 权限判断
	needAuth := false
	for _, v := range includeTypes {
		if v == "yc_planet_learn_practice" {
			needAuth = true
			break
		}
	}
	if needAuth {
		res, err = services.Get(c).SpecialCourse.GetAuthSpecialList(transport.GinCtx2Ctx(c), userID, res)
	}
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}

// SpecialListForPad 获取再售的专项课 & 用户已有的专项课.
func (t *totalReview) SpecialListForPad(c *gin.Context) {
	userID := c.GetHeader(uid)
	if err := t.valid.CheckFields(
		t.valid.Check("uid", userIDCheck, userID),
	); err != nil {
		err.ParamsErr(c)
		return
	}
	currentCourseID := c.Query("courseId")
	excludeType := c.Query("excludeType")
	includeType := c.Query("includeType")
	excludeTypes, includeTypes := lib.FilterTypes(excludeType, includeType)
	// 进行合并
	res, err := services.Get(c).SpecialCourse.SpecialList(transport.GinCtx2Ctx(c), userID, currentCourseID, excludeTypes, includeTypes)
	//2. 权限判断
	res, err = services.Get(c).SpecialCourse.GetAuthSpecialList(transport.GinCtx2Ctx(c), userID, res)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}

// SpecialListPadSupport 获取再售的专项课 & 用户已有的专项课.
func (t *totalReview) SpecialListPadSupport(c *gin.Context) {
	userID := c.GetHeader(uid)
	if err := t.valid.CheckFields(
		t.valid.Check("uid", userIDCheck, userID),
	); err != nil {
		err.ParamsErr(c)
		return
	}

	// 1. 参数处理
	currentCourseID := c.Query("courseId")
	excludeType := c.Query("excludeType")
	includeType := c.Query("includeType")
	excludeTypes, includeTypes := lib.FilterTypes(excludeType, includeType)
	startTime := time.Now()

	// 2. 获取用户的专项课
	ctx := transport.GinCtx2Ctx(c)
	specialCourseList, specialCourseIds, err := services.Get(c).SpecialCourse.SpecialListForUser(ctx, userID, currentCourseID, excludeTypes, includeTypes, true)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	durationSpecialListForUser := time.Since(startTime).Milliseconds()

	// 3. 并发处理其他逻辑-补充信息
	group1 := errgroup.WithContext(ctx)
	var (
		sortDt                 map[string]*time.Time
		result                 *vo.CourseMallSpecialListVo
		mutex                  sync.Mutex
		durationGetSortTime    int64
		durationSupplementInfo int64
	)

	group1.Go(func(ctx context.Context) error {
		// 3.1 获取用户的专项课的排序时间
		t2 := time.Now()
		data, err := services.Get(c).SpecialCourse.SpecialListOrderGetTime(ctx, userID, specialCourseIds)
		durationGetSortTime = time.Since(t2).Milliseconds()
		if err != nil {
			return err
		}
		mutex.Lock()
		sortDt = data
		mutex.Unlock()
		return nil
	})

	group1.Go(func(ctx context.Context) error {
		// 3.2 获取专项课的补充信息
		t3 := time.Now()
		res, err := services.Get(c).SpecialCourse.SpecialCourseListSupplementInfo(ctx, specialCourseList, specialCourseIds, excludeTypes, includeTypes)
		durationSupplementInfo = time.Since(t3).Milliseconds()
		if err != nil {
			return err
		}
		mutex.Lock()
		result = res
		mutex.Unlock()
		return nil
	})

	if err := group1.Wait(); err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}

	// 日志记录，注意脱敏处理
	logCli := log.NewHelper(setup.GoLoggerKratos)
	logCli.Infof("SpecialListPadSupport: userID:%s, durationSpecialListForUser:%dms, durationGetSortTime:%dms, durationSupplementInfo:%dms", maskUserID(userID), durationSpecialListForUser, durationGetSortTime, durationSupplementInfo)

	// 4. 排序处理
	result.Data = services.Get(c).SpecialCourse.SpecialListOrderHandle(ctx, result.Data, sortDt)

	c.JSON(http.StatusOK, result)
}

// maskUserID 对用户ID进行脱敏处理
func maskUserID(userID string) string {
	if len(userID) <= 4 {
		return "****"
	}
	return userID[:2] + "****" + userID[len(userID)-2:]
}

// TotalReviewTabSupport 总复习模块 tab展示逻辑
func (t *totalReview) TotalReviewTabSupport(c *gin.Context) {
	userID := c.GetHeader(uid)
	stageId := c.Query("stageId")
	subjectId := c.Query("subjectId")
	regionCode := c.Query("regionCode")
	if err := t.valid.CheckFields(
		t.valid.Check("stageId", "required,gt=0", cast.ToInt64(stageId)),
		t.valid.Check("subjectId", "required,gt=0", cast.ToInt64(subjectId)),
		// t.valid.Check("regionCode", "required,len=6", regionCode),
	); err != nil {
		err.ParamsErr(c)
		return
	}

	ctx := c.Request.Context()
	res, err := services.Get(c).TotalReview.GetTotalReviewTabSupport(ctx, userID, cast.ToInt64(stageId), cast.ToInt64(subjectId), regionCode)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}

// TotalReviewTabRedPoint 总复习模块 tab红点逻辑
func (t *totalReview) TotalReviewTabRedPoint(c *gin.Context) {
	userID := c.GetHeader(uid)
	stageId := c.Query("stageId")
	subjectId := c.Query("subjectId")
	regionCode := c.Query("regionCode")
	packageId := c.Query("packageId")
	if err := t.valid.CheckFields(
		t.valid.Check("stageId", "required,gt=0", cast.ToInt64(stageId)),
		t.valid.Check("subjectId", "required,gt=0", cast.ToInt64(subjectId)),
		t.valid.Check("packageId", "required,gt=0", packageId),
	); err != nil {
		err.ParamsErr(c)
		return
	}
	ctx := c.Request.Context()
	res, err := services.Get(c).TotalReview.TotalReviewTabRedPoint(ctx, userID, cast.ToInt64(stageId), cast.ToInt64(subjectId), regionCode, packageId)
	if err != nil {
		c.JSON(ecode.HTTPCodeAndReason(err))
		return
	}
	c.JSON(http.StatusOK, res)
}
`

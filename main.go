package main

import (
	"ai_cr/conf"
	"ai_cr/router"
	"ai_cr/setup"

	"github.com/gin-gonic/gin"
)

func RecoveryMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				c.JSON(500, gin.H{"error": "Internal Server Error"})
				c.Abort()
			}
		}()
		c.Next()
	}
}

func main() {
	InitService()
	g := gin.Default()
	g.Use(RecoveryMiddleware()) // 增加防止异常退出的中间件
	r := g.Group("/ai_cr")
	router.InitRouter(r)
	g.Run("0.0.0.0:8000")
}

func InitService() {
	//直接出厂化数据
	if err := conf.StartConfig("./conf/"); err != nil {
		panic(err)
	}
	setup.InitLog()
	setup.InitDaubaoApi()
	setup.InitGitlabClient()
	setup.InitPg()
}
